"use node";

import { v } from "convex/values";
import { action } from "../_generated/server";
import { api } from "../_generated/api";
import { encrypt, decrypt } from "../lib/crypto";

export const addRepositoryWithSecrets = action({
  args: {
    installationId: v.string(),
    url: v.string(),
    name: v.optional(v.string()),
    repo_created_at: v.optional(v.number()),
    secrets: v.optional(v.array(
      v.object({
        key: v.string(),
        value: v.string(),
      })
    )),
  },
  handler: async (ctx, args) => {
    const masterKey = process.env.ENCRYPTION_MASTER_KEY;
    if (!masterKey) {
      throw new Error("ENCRYPTION_MASTER_KEY not configured");
    }
    
    // Encrypt secrets with master key
    let encryptedSecrets: Array<{ key: string; value: string }> = [];
    if (args.secrets && args.secrets.length > 0) {
      encryptedSecrets = args.secrets.map((secret) => ({
        key: secret.key,
        value: encrypt(secret.value, masterKey),
      }));
    }
    
    // Call mutation with encrypted data
    return await ctx.runMutation(api.repositories.mutations.addRepository, {
      installationId: args.installationId,
      url: args.url,
      name: args.name,
      repo_created_at: args.repo_created_at,
      encryptedSecrets,
    });
  },
});

export const decryptRepositorySecrets = action({
  args: {
    repositoryId: v.id("repositories"),
  },
  handler: async (ctx, args) => {
    const repo = await ctx.runQuery(api.repositories.queries.getRepository, { id: args.repositoryId });
    
    if (!repo || !repo.secrets) {
      return [];
    }
    
    const masterKey = process.env.ENCRYPTION_MASTER_KEY;
    if (!masterKey) {
      throw new Error("ENCRYPTION_MASTER_KEY not configured");
    }
    
    // Decrypt secrets with master key
    return repo.secrets.map((secret: any) => ({
      key: secret.key,
      value: decrypt(secret.value, masterKey),
    }));
  },
});

export const updateRepositorySecrets = action({
  args: {
    repositoryId: v.id("repositories"),
    secrets: v.array(
      v.object({
        key: v.string(),
        value: v.string(),
      })
    ),
  },
  handler: async (ctx, args) => {
    const masterKey = process.env.ENCRYPTION_MASTER_KEY;
    if (!masterKey) {
      throw new Error("ENCRYPTION_MASTER_KEY not configured");
    }
    
    // Encrypt secrets with master key
    const encryptedSecrets = args.secrets.map((secret) => ({
      key: secret.key,
      value: encrypt(secret.value, masterKey),
    }));
    
    // Update the repository directly
    await ctx.db.patch(args.repositoryId, {
      secrets: encryptedSecrets,
    });
    
    console.log(`✅ Updated ${encryptedSecrets.length} secrets for repository`);
    return { success: true };
  },
});