import { v } from "convex/values";
import { query } from "../_generated/server";

export const getRepositoryById = query({
  args: { id: v.id("repositories") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

export const getRepositoryAndInstallationId = query({
  args: { id: v.id("repositories") },
  handler: async (ctx, args) => {
    const repo = await ctx.db.get(args.id);
    if (!repo) return null;
    
    if (!repo.integration_id) {
      return {
        ...repo,
        installation_id: null,
      };
    }
    
    const integration = await ctx.db.get(repo.integration_id);
    if (!integration) {
      return {
        ...repo,
        installation_id: null,
      };
    }
    
    return {
      ...repo,
      installation_id: integration.data?.installation_id || null,
    };
  },
});

export const getRepositories = query({
  args: { clerkOrgId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("repositories")
      .withIndex("by_clerk_org_id", (q) => q.eq("clerk_org_id", args.clerkOrgId))
      .collect();
  },
});

export const getRepositoriesByIntegrationId = query({
  args: { integrationId: v.id("integrations") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("repositories")
      .withIndex("by_integration_id", (q) => q.eq("integration_id", args.integrationId))
      .collect();
  },
});