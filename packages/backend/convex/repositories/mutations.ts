import { v } from "convex/values";
import { mutation } from "../_generated/server";

export const addRepository = mutation({
  args: {
    installationId: v.string(),
    url: v.string(),
    name: v.optional(v.string()),
    repo_created_at: v.optional(v.number()),
    encryptedSecrets: v.array(
      v.object({
        key: v.string(),
        value: v.string(), // Pre-encrypted by action
      })
    ),
  },
  handler: async (ctx, args) => {
    const integration = await ctx.db
      .query("integrations")
      .filter((q) =>
        q.and(
          q.eq(q.field("name"), "github"),
          q.eq(q.field("data.installation_id"), args.installationId)
        )
      )
      .first();

    if (!integration) {
      throw new Error(`No GitHub integration found for installation ${args.installationId}`);
    }

    const existingRepo = await ctx.db
      .query("repositories")
      .withIndex("by_clerk_org_id_and_integration_id")
      .filter((q) =>
        q.and(
          q.eq(q.field("clerk_org_id"), integration.clerk_org_id),
          q.eq(q.field("integration_id"), integration._id),
          q.eq(q.field("url"), args.url)
        )
      )
      .first();

    if (existingRepo) {
      console.log(`Repository already exists: ${args.url}`);
      return existingRepo._id;
    }

    const repoId = await ctx.db.insert("repositories", {
      url: args.url,
      clerk_org_id: integration.clerk_org_id,
      name: args.name,
      repo_created_at: args.repo_created_at,
      integration_id: integration._id,
      secrets: args.encryptedSecrets,
    });

    console.log(`✅ Added repository: ${args.url}`);
    return repoId;
  },
});

export const updateRepositoryById = mutation({
  args: {
    id: v.id("repositories"),
    data: v.object({
      url: v.optional(v.string()),
      modal_snapshot_id: v.optional(v.string()),
      name: v.optional(v.string()),
      repo_created_at: v.optional(v.number()),
      secrets: v.optional(v.array(v.object({
        key: v.string(),
        value: v.string(),
      }))),
    }),
  },
  handler: async (ctx, args) => {
    const repo = await ctx.db.get(args.id);
    if (!repo) {
      throw new Error("Repository not found");
    }

    await ctx.db.patch(args.id, args.data);
    console.log(`✅ Updated repository ${args.id}`);
    return { success: true };
  },
});

