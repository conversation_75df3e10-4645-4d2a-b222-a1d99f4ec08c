import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { api } from "./_generated/api";

const http = httpRouter();

http.route({
  path: "/webhooks/github",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const signature = request.headers.get("x-hub-signature-256");
    const eventType = request.headers.get("x-github-event");
    const deliveryId = request.headers.get("x-github-delivery");

    console.log(`GitHub webhook received: ${eventType} (delivery: ${deliveryId})`);

    const payload = await request.json();

    if (eventType === "installation") {
      const action = payload.action;
      const installation = payload.installation || {};
      const account = installation.account || {};

      const installationId = String(installation.id);
      const accountLogin = account.login;
      const accountType = account.type;

      console.log(`Installation event: ${action}`);
      console.log(`Account: ${accountLogin} (${accountType})`);

      if (action === "created") {
        await ctx.runMutation(api.integrations.mutations.createIntegration, {
          installationId,
          accountLogin,
          accountType,
        });
      } else if (action === "deleted") {
        await ctx.runMutation(api.integrations.mutations.deleteIntegrationByInstallationId, {
          installationId,
          accountLogin,
        });
      }
    } else {
      console.warn(`Unhandled event type: ${eventType}`);
    }

    return new Response(JSON.stringify({ status: "success", event: eventType }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  }),
});

export default http;