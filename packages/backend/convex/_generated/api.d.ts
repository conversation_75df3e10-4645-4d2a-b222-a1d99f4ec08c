/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as github_actions from "../github/actions.js";
import type * as http from "../http.js";
import type * as integrations_mutations from "../integrations/mutations.js";
import type * as integrations_queries from "../integrations/queries.js";
import type * as lib_auth from "../lib/auth.js";
import type * as lib_crypto from "../lib/crypto.js";
import type * as lib_utils from "../lib/utils.js";
import type * as organizations from "../organizations.js";
import type * as repositories_actions from "../repositories/actions.js";
import type * as repositories_mutations from "../repositories/mutations.js";
import type * as repositories_queries from "../repositories/queries.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  "github/actions": typeof github_actions;
  http: typeof http;
  "integrations/mutations": typeof integrations_mutations;
  "integrations/queries": typeof integrations_queries;
  "lib/auth": typeof lib_auth;
  "lib/crypto": typeof lib_crypto;
  "lib/utils": typeof lib_utils;
  organizations: typeof organizations;
  "repositories/actions": typeof repositories_actions;
  "repositories/mutations": typeof repositories_mutations;
  "repositories/queries": typeof repositories_queries;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
