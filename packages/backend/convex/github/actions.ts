"use node";

import { action } from "../_generated/server";
import { v } from "convex/values";
import { App } from "octokit";
import { api } from "../_generated/api";

const createGitHubApp = () => {
  const appId = process.env.GITHUB_APP_ID;
  const privateKey = process.env.GITHUB_PRIVATE_KEY;

  if (!appId || !privateKey) {
    throw new Error("Missing GitHub App credentials");
  }

  return new App({ appId, privateKey });
};

export const getInstallationRepositories = action({
  args: {
    installationId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      console.info(`🔑 Creating GitHub App client for installation: ${args.installationId}`);
      
      const app = createGitHubApp();
      const octokit = await app.getInstallationOctokit(Number(args.installationId));
      
      console.info("✅ Successfully authenticated with GitHub");

      const { data } = await octokit.rest.apps.listReposAccessibleToInstallation({
        per_page: 100,
      });

      console.info(`✅ Fetched ${data.repositories.length} repositories`);
      
      return {
        repositories: data.repositories,
        total_count: data.total_count,
      };
    } catch (error) {
      console.error("❌ Error fetching repositories:", error);
      throw error;
    }
  },
});



// Repository-related actions have been moved to repositories/actions.ts