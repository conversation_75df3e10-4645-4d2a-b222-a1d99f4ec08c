import { v } from "convex/values";
import { mutation } from "../_generated/server";

export const createIntegration = mutation({
  args: {
    installationId: v.string(),
    accountLogin: v.string(),
    accountType: v.string(),
  },
  handler: async (ctx, args) => {
    const existingIntegration = await ctx.db
      .query("integrations")
      .withIndex("by_clerk_org_id")
      .filter((q) =>
        q.and(
          q.eq(q.field("name"), "github"),
          q.eq(q.field("data.installation_id"), args.installationId),
          q.eq(q.field("data.github_org_name"), args.accountLogin)
        )
      )
      .first();

    if (existingIntegration) {
      console.log(`Integration already exists for ${args.accountLogin}`);
      return;
    }

    const integrationData = {
      installation_id: args.installationId,
      github_org_name: args.accountLogin,
      github_org_type: args.accountType,
    };

    await ctx.db.insert("integrations", {
      name: "github",
      clerk_org_id: "pending", // This will need to be updated when we link to a Clerk org
      data: integrationData,
    });

    console.log(`✅ Created new GitHub integration for ${args.accountLogin}`);
  },
});

export const deleteIntegrationById = mutation({
  args: {
    id: v.id("integrations"),
  },
  handler: async (ctx, args) => {
    const integration = await ctx.db.get(args.id);
    if (!integration) {
      throw new Error(`Integration ${args.id} not found`);
    }
    
    await ctx.db.delete(args.id);
    console.log(`✅ Deleted integration ${args.id}`);
    return { success: true };
  },
});

export const deleteIntegrationByInstallationId = mutation({
  args: {
    installationId: v.string(),
    accountLogin: v.string(),
  },
  handler: async (ctx, args) => {
    const integration = await ctx.db
      .query("integrations")
      .withIndex("by_clerk_org_id")
      .filter((q) =>
        q.and(
          q.eq(q.field("name"), "github"),
          q.eq(q.field("data.installation_id"), args.installationId),
          q.eq(q.field("data.github_org_name"), args.accountLogin)
        )
      )
      .first();

    if (integration) {
      const githubOrgName = integration.data?.github_org_name || args.accountLogin;
      console.log(`🗑️ Uninstalling GitHub App for organization: ${githubOrgName}`);

      await ctx.db.delete(integration._id);
      console.log(`✅ Deleted integration for ${githubOrgName}`);
    } else {
      console.warn(
        `No integration found for installation ${args.installationId} and org ${args.accountLogin}`
      );
    }
  },
});