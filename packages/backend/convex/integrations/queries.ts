import { v } from "convex/values";
import { query } from "../_generated/server";

export const getIntegrationById = query({
  args: { id: v.id("integrations") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

export const getIntegrations = query({
  args: { clerkOrgId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("integrations")
      .withIndex("by_clerk_org_id", (q) => q.eq("clerk_org_id", args.clerkOrgId))
      .collect();
  },
});

export const getIntegrationByInstallationId = query({
  args: { installationId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("integrations")
      .filter((q) =>
        q.and(
          q.eq(q.field("name"), "github"),
          q.eq(q.field("data.installation_id"), args.installationId)
        )
      )
      .first();
  },
});