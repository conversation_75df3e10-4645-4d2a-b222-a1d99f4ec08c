import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

const ScanStatus = v.union(
  v.literal("running"),
  v.literal("completed"),
  v.literal("failed"),
  v.literal("cancelled"),
);

const IssueStatus = v.union(
  v.literal("suggested"),
  v.literal("pending"),
  v.literal("running"),
  v.literal("completed"),
  v.literal("failed"),
  v.literal("cancelled"),
);

export default defineSchema({
  // DEMO SCHEMA //

  notes: defineTable({
    userId: v.string(), // Clerk user ID
    title: v.string(),
    content: v.string(),
    summary: v.optional(v.string()),
  }).index("by_user_id", ["userId"]),

  // BACKSPACE SCHEMA //

  organizations: defineTable({
    clerk_org_id: v.string(), // Clerk organization ID
    settings: v.optional(v.any()), // Custom org settings
    billing_tier: v.optional(v.string()),
    created_by: v.string(), // Clerk user ID
  }).index("by_clerk_org_id", ["clerk_org_id"]),

  integrations: defineTable({
    name: v.optional(v.string()),
    clerk_org_id: v.string(), // Clerk organization ID
    data: v.optional(v.any()), // JSONB data
  }).index("by_clerk_org_id", ["clerk_org_id"]),

  repositories: defineTable({
    url: v.string(), // NOT NULL in original schema
    modal_snapshot_id: v.optional(v.string()),
    clerk_org_id: v.string(), // Clerk organization ID
    name: v.optional(v.string()),
    repo_created_at: v.optional(v.number()), // timestamp as number
    integration_id: v.optional(v.id("integrations")),
    secrets: v.optional(v.array(
      v.object({
        key: v.string(),
        value: v.string(), // Encrypted with master key
      })
    )),
  })
    .index("by_clerk_org_id", ["clerk_org_id"])
    .index("by_integration_id", ["integration_id"])
    .index("by_clerk_org_id_and_integration_id", [
      "clerk_org_id",
      "integration_id",
    ]),

  codemaps: defineTable({
    repo_id: v.id("repositories"),
    ast: v.optional(
      v.object({
        files: v.array(
          v.object({
            path: v.string(),
            imports: v.array(v.string()),
            exports: v.array(v.string()),
          }),
        ),
      }),
    ),
    graph: v.optional(
      v.object({
        nodes: v.array(
          v.object({
            id: v.string(),
            label: v.string(),
            type: v.string(),
            subgraph: v.string(),
            rootPath: v.string(),
          }),
        ),
        edges: v.array(
          v.object({
            source: v.string(),
            target: v.string(),
            label: v.string(),
          }),
        ),
        subgraphs: v.array(
          v.object({
            id: v.string(),
            label: v.string(),
            nodes: v.array(v.string()),
          }),
        ),
      }),
    ),
    metadata: v.optional(v.any()),
  }).index("by_repo_id", ["repo_id"]),

  scans: defineTable({
    repo_id: v.id("repositories"),
    branch: v.string(),
    status: ScanStatus,
    output: v.optional(
      v.object({
        report: v.string(), // markdown
        issues: v.array(v.id("issues")),
        metrics: v.optional(v.any()), // save scan metrics by type (e.g. clarity, modularity, etc.)
        metadata: v.optional(v.any()), // track intermediary outputs here
      }),
    ),
  }).index("by_repo_id", ["repo_id"]),

  issues: defineTable({
    issue_id: v.string(), // id for orchestrating issue execution
    title: v.string(),
    description: v.string(),
    status: IssueStatus,
    sub_issues: v.array(
      v.object({
        sub_issue_id: v.string(),
        title: v.string(),
        description: v.string(),
      }),
    ),
    dependencies_ids: v.array(v.string()), // Array of issue_id's
    scan_id: v.id("scans"),
    metadata: v.optional(v.any()),
  }).index("by_scan_id", ["scan_id"]),
});
