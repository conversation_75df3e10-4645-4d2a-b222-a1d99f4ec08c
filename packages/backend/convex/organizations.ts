import { query } from "./_generated/server";
import type { Auth } from "convex/server";

export const getUserId = async (ctx: { auth: Auth }) => {
  return (await ctx.auth.getUserIdentity())?.subject;
};

export const getOrganization = query({
  args: {},
  handler: async (ctx) => {
    const orgId = await getUserId(ctx);
    if (!orgId) return null;

    const org = await ctx.db
      .query("organizations")
      .filter((q) => q.eq(q.field("clerk_org_id"), orgId))
      .collect();

    return org;
  },
});
