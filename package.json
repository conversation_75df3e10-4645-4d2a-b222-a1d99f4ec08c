{"name": "convex-monorepo", "private": true, "scripts": {"dev": "turbo run dev", "build": "turbo run build", "clean": "turbo run clean && rm -rf node_modules", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\" --ignore-path .gitignore"}, "devDependencies": {"prettier": "^3.2.5", "turbo": "^2.0.12"}, "engines": {"node": ">=18.8.0"}, "workspaces": ["apps/*", "packages/*"], "packageManager": "npm@9.8.1", "dependencies": {"convex": "^1.25.0"}}