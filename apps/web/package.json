{"name": "web-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/clerk-react": "^5.32.1", "@clerk/nextjs": "^6.23.0", "@headlessui/react": "^2.0.3", "@heroicons/react": "^2.1.3", "@packages/backend": "*", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "convex": "^1.13.0", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "next": "^15.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^20.12.11", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.4", "eslint": "^8.57.0", "eslint-config-next": "^15.2.3", "postcss": "^8.4.38", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "^5.7.3"}}