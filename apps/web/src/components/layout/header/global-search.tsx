"use client";

import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useState } from "react";

export function GlobalSearch({ className }: { className?: string }) {
  const [query, setQuery] = useState("");

  const handleSearch = () => {
    if (!query.trim()) return;

    // later: trigger spotlight, route, or fetch
    console.log("Global search for:", query);
  };

  return (
    <div className={cn("relative w-[20rem]", className)}>
      <Search className="absolute right-[1rem] top-1/2 size-[1.4rem] -translate-y-1/2 text-base-700" />
      <Input
        placeholder="Search"
        className="pr-[3rem] pl-[1rem] w-full rounded-[0.2rem] border-0"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            e.preventDefault();
            handleSearch();
          }
        }}
      />
    </div>
  );
}
