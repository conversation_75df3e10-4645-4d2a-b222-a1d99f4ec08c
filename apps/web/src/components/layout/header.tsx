
import { cn } from "@/lib/utils";
import { CommandGroup, FilterItem, FilterPopover } from "../ui/filter-popopver"
import { SidebarMenuButton } from "../ui/sidebar";
import { ChevronsUpDown, Search } from "lucide-react";
import { GlobalSearch } from "./header/global-search";

const dummyOrgs = [
  {
    id: 1,
    name: "Backspace",
    slug: "backspace",
  },
  {
    id: 2,
    name: "Main App",
    slug: "main-app",
  },
]

const currentOrg = dummyOrgs[0]


const orgTrigger = (
  <SidebarMenuButton className='
    cursor-pointer w-auto justify-between 
    transition-colors
    hover:!bg-transparent
    focus:!bg-transparent
    focus-visible:!bg-transparent
    focus-visible:!ring-0
    active:!bg-transparent 
    data-[state=open]:text-base-25 
    data-[state=open]:dark:text-base-25'
  >
    <div className='flex items-center gap-2'>
      <span className='truncate tx-body font-medium'>{currentOrg.name}</span>
    </div>
    <ChevronsUpDown className='!size-[1.2rem] opacity-50' />
  </SidebarMenuButton>
  );

export default function Header() {
  return (
    <header className="sticky top-0 z-10 flex justify-between px-[2rem] py-[1.5rem] border-b-[0.1rem]">
      <div className="flex items-center gap-[1.6rem]">
        <FilterPopover
        trigger={orgTrigger}
        searchPlaceholder='Search orgs...'
        contentWidth='w-full'
        align='start'
        >
          <CommandGroup>
            {dummyOrgs.map((org) => (
              <FilterItem
                key={org.id}
                label={org.name}
                isSelected={org.id === currentOrg.id}
                onToggle={() => { console.log('test')}}
              />
            ))}
          </CommandGroup>
        </FilterPopover>
        <GlobalSearch/>
      </div>
      <div className="flex items-center">
          
      </div>
    </header>
  )
}