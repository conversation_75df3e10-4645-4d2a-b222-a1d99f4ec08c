"use client"
import {
  Calendar, FolderOpen, Home, Inbox, Monitor, Search, Settings
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar"
import Link from "next/link";
import Logo from "../svg/logo";
import { usePathname } from "next/navigation";

export function AppSidebar() {
  const pathname = usePathname(); // Todo: Get current pathname from context
  const { state } = useSidebar();
  const currentOrgSlug = "backspace"; // Todo: Get current org slug from context

  const navPrefix = `/dashboard`
  // Menu items.
  const mainNavItems = [
    { title: 'Inbox', url: `${navPrefix}/${currentOrgSlug}/inbox`, icon: Inbox },
    {
      title: 'Repositories',
      url: `${navPrefix}/${currentOrgSlug}/repositories`,
      icon: FolderOpen
    },
    { title: 'Monitor', url: `${navPrefix}/${currentOrgSlug}/monitor`, icon: Monitor },
    {
      title: 'Settings',
      url: `${navPrefix}/${currentOrgSlug}/settings`,
      icon: Settings
    }
  ];

  const isActivePath = (item: { title: string; url: string }) => {
    if (item.title === 'Settings') return pathname.includes('/settings');
    if (item.title === 'Scans') return pathname.includes('/scans');
    return pathname === item.url;
  };

  return (
    <Sidebar collapsible='icon'>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="flex items-center px-[2rem] py-[1.9rem] box-border min-h-[6rem]">
              <SidebarTrigger className='size-[2.4rem] p-0 border-0 cursor-pointer' />
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {mainNavItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    variant='outline'
                    tooltip={item.title}
                    isActive={isActivePath(item)}
                    className='data-[active=true]:bg-sidebar-primary  data-[active=true]:dark:bg-sidebar-primary'
                  >
                    <Link
                      href={item.url}
                      className='text-base-25 p-[0.9rem] w-full flex gap-[0.8rem] items-center box-border hover:[&>span]:opacity-100 active:[&>span]:opacity-100 data-[active=true]:[&>span]:opacity-100 hover:[&>svg]:opacity-100 data-[active=true]:[&>svg]:opacity-100'
                    >
                      <item.icon className='size-[1.2rem] opacity-50 transition-opacity duration-100' />
                      <span className='font-normal opacity-50 transition-opacity duration-100 box-border'>
                        {item.title}
                      </span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}