"use client";
import * as React from "react";
import { cn } from "@/lib/utils";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  hint?: string;
  leading?: React.ReactNode;
  trailing?: React.ReactNode;
}

export const CustomInput = React.forwardRef<HTMLInputElement, InputProps>(
  ({ label, hint, leading, trailing, className, ...props }, ref) => {
    const inputId =
      props.id ?? `input-${label.replace(/\s+/g, "-").toLowerCase()}`;

    return (
      <div className={cn("flex flex-col gap-[0.6rem] box-border")}>
        <label htmlFor={inputId} className="tx-body flex items-center gap-1">
          {label}
          {props.required && <span className="text-red-600">*</span>}
        </label>

        <div className="relative w-fit flex items-center">
          {leading && leading}

          <input
            ref={ref}
            id={inputId}
            className={cn(
              "w-[32rem] border-[0.1rem] border-base-925 shadow-xs bg-transparent",
              className,
              "pr-[3.5rem] py-[0.6rem] tx-body transition-[color,box-shadow] box-border outline-none",
              "focus-visible:border-ring focus-visible:ring-base-800 focus-visible:ring-[0.1rem]",
              "placeholder:text-base-700 placeholder:font-normal",
              props.disabled && "opacity-50 cursor-not-allowed",
            )}
            {...props}
          />

          {trailing && (
            <div className="absolute right-[1rem] flex items-center">
              {trailing}
            </div>
          )}
        </div>

        {hint && <p className="text-body-sm text-base-400">{hint}</p>}
      </div>
    );
  },
);
CustomInput.displayName = "CustomInput";
