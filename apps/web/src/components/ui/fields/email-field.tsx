"use client";
import * as React from "react";
import { cn } from "@/lib/utils";
import { CustomInput } from "./custom-input";
import { Tooltip } from "../tooltip";

import { Mail, HelpCircle } from "lucide-react";

interface EmailFieldProps {
  label: string;
  hint?: string;
  placeholder?: string;
  required?: boolean;
  className?: string;
}

export const EmailField = ({
  label,
  hint,
  placeholder,
  required = false,
  className,
}: EmailFieldProps) => {
  const [value, setValue] = React.useState("");
  const [touched, setTouched] = React.useState(false);

  const isValid = value === "" ? !required : /^\S+@\S+\.\S+$/.test(value);

  const leading = (
    <span className="absolute left-[1rem] flex items-center pointer-events-none">
      <Mail size={"2rem"} className="text-base-25" />
    </span>
  );

  return (
    <div className={cn("flex flex-col gap-[0.6rem]", className)}>
      <CustomInput
        label={label}
        type="email"
        placeholder={placeholder}
        required={required}
        value={value}
        className="pl-[3.7rem]"
        onChange={(e) => setValue(e.currentTarget.value)}
        onBlur={() => setTouched(true)}
        leading={leading}
        trailing={
          hint ? (
            <Tooltip content={hint}>
              <button
                type="button"
                aria-label="Help"
                className="inline-flex items-center justify-center"
              >
                <HelpCircle size={"2rem"} className="text-current" />
              </button>
            </Tooltip>
          ) : undefined
        }
      />

      {touched && !isValid && (
        <p className="tx-body-xs text-base-700">Please enter a valid email.</p>
      )}
    </div>
  );
};

EmailField.displayName = "EmailField";
