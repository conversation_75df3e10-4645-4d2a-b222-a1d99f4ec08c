"use client";
import * as React from "react";
import { cn } from "@/lib/utils";
import { CustomInput } from "./custom-input";
import { Tooltip } from "../tooltip";
import { CountrySelector, COUNTRIES } from "../country-selector";
import { HelpCircle } from "lucide-react";

interface PhoneFieldProps {
  label: string;
  hint?: string;
  placeholder?: string;
  required?: boolean;
  className?: string;
}

export const PhoneField = ({
  label,
  hint,
  placeholder,
  required = false,
  className,
}: PhoneFieldProps) => {
  const [country, setCountry] = React.useState(COUNTRIES[0]);
  const [value, setValue] = React.useState("");
  const [touched, setTouched] = React.useState(false);

  // pattern: country.dial (like "+1") then at least 4 more digits/spaces/etc
  const pattern = new RegExp(`^\\${country.dial}[0-9 ()-]{4,}$`);
  const isValid = value === "" ? !required : pattern.test(value);

  const leading = (
    <span className="absolute flex items-center pointer-events-all z-10">
      <CountrySelector
        value={country.code}
        onChange={(code) =>
          setCountry(COUNTRIES.find((c) => c.code === code) ?? COUNTRIES[0])
        }
      />
    </span>
  );
  return (
    <div className={cn("flex flex-col gap-[0.6rem]", className)}>
      <CustomInput
        label={label}
        type="tel"
        placeholder={`${country.dial} ${placeholder}`}
        required={required}
        value={value}
        onChange={(e) => setValue(e.currentTarget.value)}
        className="pl-[5.5rem]"
        onBlur={() => setTouched(true)}
        leading={leading}
        trailing={
          hint ? (
            <Tooltip content={hint}>
              <button
                type="button"
                aria-label="Help"
                className="inline-flex items-center justify-center"
              >
                <HelpCircle size={"2rem"} className="text-current" />
              </button>
            </Tooltip>
          ) : undefined
        }
      />

      {touched && !isValid && (
        <p className="tx-body-xs text-base-700">
          Please enter a valid phone number starting with {country.dial}.
        </p>
      )}
    </div>
  );
};

PhoneField.displayName = "PhoneField";
