// components/ui/fields/UrlField.tsx
"use client";
import * as React from "react";
import { cn } from "@/lib/utils";
import { CustomInput } from "./custom-input";
import { Tooltip } from "../tooltip";
import { HelpCircle } from "lucide-react";

interface UrlFieldProps {
  label: string;
  hint?: string;
  placeholder?: string;
  required?: boolean;
  className?: string;
}

export const UrlField = ({
  label,
  hint,
  placeholder,
  required = false,
  className,
}: UrlFieldProps) => {
  const [value, setValue] = React.useState("");
  const [touched, setTouched] = React.useState(false);

  // HTML5 “type=url” helps, plus this regex
  const isValid =
    value === "" ? !required : /^https?:\/\/\S+\.\S+$/.test(value);

  const leading = (
    <span className="absolute flex items-center pointer-events-none">
      <span className="border-e-[0.1rem] border-base-925 px-[1rem] py-[0.6rem] box-border">
        <span className="tx-body font-normal text-base-700">https://</span>
      </span>
    </span>
  );

  return (
    <div className={cn("flex flex-col gap-[0.6rem]", className)}>
      <CustomInput
        label={label}
        type="url"
        placeholder={placeholder}
        required={required}
        value={value}
        className="pl-[7.5rem]"
        onChange={(e) => setValue(e.currentTarget.value)}
        onBlur={() => setTouched(true)}
        leading={leading}
        trailing={
          hint ? (
            <Tooltip content={hint}>
              <button
                type="button"
                aria-label="Help"
                className="inline-flex items-center justify-center"
              >
                <HelpCircle size="20" className="text-current" />
              </button>
            </Tooltip>
          ) : undefined
        }
      />

      {touched && !isValid && (
        <p className="tx-body-xs text-base-700">
          Please enter a valid URL (starting with http:// or https://).
        </p>
      )}
    </div>
  );
};
