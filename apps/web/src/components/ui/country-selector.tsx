"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import * as Select from "@radix-ui/react-select";
import { ChevronDown } from "lucide-react";

interface Country {
  code: string;
  label: string;
  dial: string;
}

export const COUNTRIES: Country[] = [
  { code: "us", label: "US", dial: "+1" },
  { code: "ca", label: "CA", dial: "+1" },
  { code: "uk", label: "UK", dial: "+44" },
];

export function CountrySelector({
  value,
  onChange,
}: {
  value: string;
  onChange: (code: string) => void;
}) {
  return (
    <Select.Root value={value} onValueChange={onChange}>
      <Select.Trigger
        className={cn(
          "inline-flex items-center justify-between gap-1 pl-[1rem] py-[0.6rem] box-border",
          "border-none bg-transparent tx-body text-base-25/100",
          "focus:outline-none focus:ring-0"
        )}
        aria-label="Select country"
      >
        <Select.Value className="pointer-events-none"></Select.Value>
        <Select.Icon className="pointer-events-none">
          <ChevronDown />
        </Select.Icon>
      </Select.Trigger>

      <Select.Content className="rounded bg-base-1000 shadow-lg">
        <Select.Viewport>
          {COUNTRIES.map((c) => (
            <Select.Item
              key={c.code}
              value={c.code}
              className={cn(
                "flex items-center justify-between gap-1 px-[1rem] py-[0.6rem] tx-body text-base-25/50 hover:text-base-25/100 cursor-pointer",
                value === c.code && "text-base-25/100",
                "focus:outline-none",                    
                "[&[data-highlighted]]:bg-transparent" 
              )}
            >
              <Select.ItemText>
                {c.label}
              </Select.ItemText>
              <Select.ItemIndicator>
                <ChevronDown />
              </Select.ItemIndicator>
            </Select.Item>
          ))}
        </Select.Viewport>
      </Select.Content>
    </Select.Root>
  );
}
