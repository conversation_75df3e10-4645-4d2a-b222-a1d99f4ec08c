import React from 'react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator
} from '@/components/ui/command';
import { Button } from '@/components/ui/button';
import { Check, Filter } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface FilterPopoverProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  searchPlaceholder?: string;
  emptyMessage?: string;
  contentWidth?: string;
  align?: 'start' | 'center' | 'end';
}

export function FilterPopover({
  trigger,
  children,
  searchPlaceholder = 'Search...',
  emptyMessage = 'No results found.',
  contentWidth = 'w-48',
  align = 'start'
}: FilterPopoverProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>{trigger}</PopoverTrigger>
      <PopoverContent className={`${contentWidth} p-0`} align={align}>
        <Command>
          <CommandInput placeholder={searchPlaceholder} className='tx-body-sm' />
          <CommandList>
            <CommandEmpty className='tx-body-sm px-[2rem] py-[1rem]'>{emptyMessage}</CommandEmpty>
            {children}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

// Helper function to create standard filter triggers
export function createFilterTrigger({
  label,
  icon: Icon = Filter
}: {
  label: string;
  icon?: React.ElementType;
}) {
  return (
    <Button variant='outline' size='sm' className='h-7 tx-body'>
      <Icon className='mr-1 h-3 w-3' />
      {label}
    </Button>
  );
}

// Helper component for filter items with standardized checkmarks
export function FilterItem({
  label,
  isSelected,
  onToggle,
  className,
  icon: Icon
}: {
  label: string;
  isSelected: boolean;
  onToggle: () => void;
  className?: string;
  icon?: React.ElementType;
}) {
  return (
    <CommandItem
      // onSelect={onToggle} // Todo: Enable this later
      className={cn('justify-between tx-body-sm', className)}
    >
      <div className='flex items-center'>
        {Icon && <Icon className='mr-2 h-3 w-3' />}
        <span>{label.replace('_', ' ')}</span>
      </div>
      <Check
        className={cn('h-3 w-3', isSelected ? 'opacity-100' : 'opacity-0')}
      />
    </CommandItem>
  );
}

// Helper component for filter actions (Select All, Clear All)
export function FilterActions({
  onSelectAll,
  onClearAll,
  hasSelections
}: {
  onSelectAll: () => void;
  onClearAll: () => void;
  hasSelections: boolean;
}) {
  return (
    <>
      <CommandSeparator />
      <CommandGroup>
        <CommandItem onSelect={onSelectAll} className='tx-body'>
          Select All
        </CommandItem>
        {hasSelections && (
          <CommandItem onSelect={onClearAll} className='tx-body'>
            Clear All
          </CommandItem>
        )}
      </CommandGroup>
    </>
  );
}

// Export the related Command components for convenience
export {
  CommandGroup,
  CommandItem,
  CommandSeparator
} from '@/components/ui/command';