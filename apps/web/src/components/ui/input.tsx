import * as React from "react";

import { cn } from "@/lib/utils";

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground shadow-xs dark:border-base-925 border-base-925 flex w-full min-w-0 border-[0.1rem] bg-transparent px-[1rem] py-[0.6rem] tx-body transition-[color] outline-none file:inline-flex file:border-0 file:bg-transparent file:tx-body-sm file:font-normal disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:tx-body",
        "focus-visible:ring-inset focus-visible:ring-[0.1rem] focus-visible:ring-base-800 focus-visible:ring-offset-0",
        "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
        className,
      )}

      {...props}
    />
  );
}

export { Input };
