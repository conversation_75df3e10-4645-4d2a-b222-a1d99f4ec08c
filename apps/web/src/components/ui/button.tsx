import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  ["inline-flex items-center justify-center rounded-[0.2rem] px-[1rem] py-[0.2rem]",].join(" "),
  {
    variants: {
      size: {
        sm: "tx-body-sm",
        md: "tx-body",
      },
      variant: {
        solid:   "bg-base-25 text-base-1000",                         
        outline: "bg-transparent border-[0.1rem] border-current/50 hover:border-current/100",
        text:    "bg-transparent p-0",
      },
      state: {
        normal:   "",
        selected: "ring-2 ring-primary-500",
      },
    },
    defaultVariants: {
      variant: "solid",
      size: "md",
      state: "normal",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant, size, state, className, asChild = false,...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        ref={ref}
        className={cn(
          buttonVariants({ variant, size, state }),
          className
        )}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
