"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Search } from "lucide-react";
import { Input } from "./input";

interface SearchBarProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onSearch?: (value: string) => void;
}

export const SearchBar = React.forwardRef<HTMLInputElement, SearchBarProps>(
  ({ onSearch, className, ...props }, ref) => {
    const [value, setValue] = React.useState("");

    const handleSearch = () => {
      const trimmed = value.trim();
      if (trimmed && onSearch) onSearch(trimmed);
    };

    return (
      <div className={
        cn("relative w-[32rem]", className)
      }>
        <Search className="absolute left-[1rem] top-1/2 size-[1.4rem] -translate-y-1/2 text-base-700" />

        <Input
          ref={ref}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
              handleSearch();
            }
          }}
          placeholder="Search"
          className={cn("pl-[3rem] pr-[1rem] w-full rounded-[0.2rem]", className)}
          {...props}
        />
      </div>
    );
  },
);

SearchBar.displayName = "SearchBar";
