import { cn } from "@/lib/utils"

const ranges = [
  { label: "1D", value: 1 },
  { label: "7D", value: 7 },
  { label: "30D", value: 30 },
]

export function DataRangeSelector({
  value,
  onChange,
}: {
  value: number | null
  onChange: (val: number | null) => void
}) {
  return (
    <div className="inline-flex overflow-hidden border-[0.1rem] border-base-800 bg-transparent text-[1.2rem]">
      {ranges.map((range) => (
        <button
          key={range.value}
          onClick={() => onChange(value === range.value ? null : range.value)}
          className={cn(
            "p-[0.5rem] transition-colors min-w-[3.8rem] cursor-pointer",
            value === range.value
              ? "bg-base-800 text-base-25"
              : "text-base-400 hover:bg-base-900"
          )}
        >
          {range.label}
        </button>
      ))}
    </div>
  )
}

