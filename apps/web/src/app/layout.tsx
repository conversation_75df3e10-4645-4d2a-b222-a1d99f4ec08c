import "./styles/globals.css";

import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON> } from "next/font/google";
import { cn } from "@/lib/utils";

import ConvexClientProvider from "./ConvexClientProvider";

const saira = Saira({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Backspace",
  description: "Backspace automates code quality and maintenance.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={cn('bg-background overflow-hidden overscroll-none font-sans antialiased',saira.className)}>
        <ConvexClientProvider>{children}</ConvexClientProvider>
      </body>
    </html>
  );
}
