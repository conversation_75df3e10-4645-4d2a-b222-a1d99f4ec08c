'use client'

import { useState } from "react";
import { subDays, isAfter, parseISO } from "date-fns"

import PageContainer from "@/components/layout/page-container";
import { SearchBar } from "@/components/ui/search-bar";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CheckboxDropdown } from "@/components/ui/table/checkbox-dropdown";
import { DataRangeSelector } from "@/components/ui/table/data-range-selector";

import { Calendar } from "lucide-react";

import { formatTimestamp } from "@/utils/format-timestamp";
import { formatRelativeTime } from "@/utils/format-relative-time";

// Todo: Remove this
// Dummy data for repo list
import { dummies } from "./dummy";



const typeFilterCta = (
  <>
    <Calendar className="size-[1.2rem] mr-[0.2rem]" />
    <span>Type</span>
  </>
)

const columnsFilterCta = (
  <>
    <Calendar className="size-[1.2rem] mr-[0.2rem]" />
    <span>Columns</span>
  </>
)

const columnsFilterContent = (<></>)

function TableFilters({
  range,
  setRange,
}: {
  range: number | null
  setRange: (val: number | null) => void
}) {
  return (
    <div className="flex justify-between items-center">
      <div className="flex gap-[0.6rem]">
        <CheckboxDropdown trigger={typeFilterCta} content={columnsFilterContent} />
        <CheckboxDropdown trigger={columnsFilterCta} content={columnsFilterContent} />
        <SearchBar className="w-[16.5rem] text-body-sm" placeholder="Search"/>
      </div>
      <div>
        <DataRangeSelector value={range} onChange={setRange} />
      </div>
    </div>
  )
}

export default function RepositoriesPage() {
  const [range, setRange] = useState<number | null>(null);

  const filteredData = range
  ? dummies.filter((item) => {
      const itemDate = parseISO(item.timeStamp);
      return isAfter(itemDate, subDays(new Date(), range));
    })
  : dummies;

  return (
    <PageContainer>
      <div>
        <TableFilters range={range} setRange={setRange} />
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[31.2rem]">Event Name</TableHead>
            <TableHead className="w-[12rem] text-center">Type</TableHead>
            <TableHead className="w-[12rem] text-center">Status</TableHead>
            <TableHead className="w-[12rem] text-center">Issues</TableHead>
            <TableHead className="w-[31.2rem]">Branch Source</TableHead>
            <TableHead className="w-[20rem]">Last Update</TableHead>
            <TableHead className="w-[15rem]">Time Stamp</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredData.map((dummy) => (
            <TableRow key={dummy.id}>
              <TableCell>{dummy.name}</TableCell>
              <TableCell className="text-center">{dummy.type}</TableCell>
              <TableCell className="text-center">{dummy.status}</TableCell>
              <TableCell className="text-center">{dummy.issues}</TableCell>
              <TableCell className="text-base-500">{dummy.branchSource}</TableCell>
              <TableCell className="text-base-500">{formatRelativeTime(dummy.timeStamp)}</TableCell>
              <TableCell className="text-base-500">{formatTimestamp(dummy.timeStamp)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </PageContainer>
  );
}
