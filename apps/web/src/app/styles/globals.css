@import "tailwindcss";
@import "tw-animate-css";
@import "./typography.css";
@import "./colors.css";

@custom-variant dark (&:is(.dark *));

@utility container {
  margin-inline: auto;
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  :root {
    --primary: var(--color-base-1000);
    --background: var(--color-base-1000);
    --foreground: var(--color-base-25);
    
    --border: var(--color-base-925);

    --ring: var(--color-base-925);

    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);

    --accent: var(--color-base-500);
    --accent-foreground: oklch(0.145 0 0);

    --sidebar: var(--color-base-1000);
    --sidebar-foreground: var(--color-base-25);
    --sidebar-primary: var(--color-base-950);
    --sidebar-accent-foreground: var(--color-base-25);
    --sidebar-accent: var(--color-base-925);
    --sidebar-accent-foreground: var(--color-base-25);
    --sidebar-border: var(--color-base-925);
    --sidebar-ring: var(--color-base-925);
  }

  .dark {
    --primary: var(--color-base-1000);
    --background: var(--color-base-1000);
    --foreground: var(--color-base-25);
    
    --border: var(--color-base-925);

    --ring: var(--color-base-925);
    
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);

    --accent: var(--color-base-500);
    --accent-foreground: oklch(0.145 0 0);

    --sidebar: var(--color-base-1000);
    --sidebar-foreground: var(--color-base-25);
    --sidebar-primary: var(--color-base-950);
    --sidebar-accent-foreground: var(--color-base-25);
    --sidebar-accent: var(--color-base-925);
    --sidebar-accent-foreground: var(--color-base-25);
    --sidebar-border: var(--color-base-925);
    --sidebar-ring: var(--color-base-925);
  }

  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    margin: 0;
    padding: 0;
  }

  html {
    box-sizing: border-box;
  }
  *, *::before, *::after {
    box-sizing: inherit;
  }
}


@theme {
  --breakpoint-*: initial;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1440px;

  --color-primary: var(--primary);

  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-border: var(--border);
  --color-ring: var(--ring);

  --foreground: var(--color-base-1000);
  
  --border: var(--color-base-925);

  --font-saira: Saira, sans-serif;
 
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

* {
  @apply border-border outline-ring/50;
}

body {
  @apply bg-background text-foreground;
}


html {
  font-family: "Saira", sans-serif;
  font-size: calc(100vw / 1440 * 10);
  background-color: var(--color-base-1000);
  color: var(--color-base-25);
}

@media (width <= theme(--breakpoint-md)) {
  html {
    font-size: calc(100vw / 375 * 10);
  }
}

body {
  font: 1.6rem/1.3 var(--font-saira);
}

@media (width > theme(--breakpoint-xl)) {
  body {
    font-size: 16px;
  }
}