#!/usr/bin/env python3
"""
Test the enhanced tool result extraction functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sandbox.modal_sandbox import _extract_tool_result

def test_enhanced_extraction():
    """Test the enhanced tool result extraction with sample data."""
    
    print("🔍 Testing Enhanced Tool Result Extraction")
    print("=" * 50)
    
    # Test 1: Read tool with <PERSON>'s analysis
    print("\n📖 Test 1: Read Tool Analysis")
    claude_response_read = """
    I can see that the package.json file contains a Next.js project with several dependencies. 
    This shows that the project is using React 18, Next.js 14, and TypeScript. The file also 
    includes development dependencies for testing with Jest and ESLint for code quality.
    
    Based on this configuration, I can identify several potential issues with the setup.
    """
    
    result = _extract_tool_result(
        tool_name="Read",
        tool_inputs={"file_path": "package.json"},
        claude_response=claude_response_read
    )
    print(f"✅ Read result: {result}")
    
    # Test 2: Bash tool with command analysis
    print("\n⚡ Test 2: Bash Tool Analysis")
    claude_response_bash = """
    The command shows that there are 15 TypeScript files in the components directory.
    This indicates a well-structured component architecture. The output reveals that
    most components are properly organized in subdirectories.
    """
    
    result = _extract_tool_result(
        tool_name="Bash",
        tool_inputs={"command": "find src/components -name '*.tsx' | wc -l"},
        claude_response=claude_response_bash
    )
    print(f"✅ Bash result: {result}")
    
    # Test 3: LS tool with directory analysis
    print("\n📁 Test 3: LS Tool Analysis")
    claude_response_ls = """
    I can see the project structure includes:
    - apps/web: The main Next.js application
    - apps/agent: Python-based agent system
    - packages/: Shared packages and utilities
    
    The directory structure follows a monorepo pattern with clear separation of concerns.
    """
    
    result = _extract_tool_result(
        tool_name="LS",
        tool_inputs={"path": "/workspace"},
        claude_response=claude_response_ls
    )
    print(f"✅ LS result: {result}")
    
    # Test 4: Edit tool with modification summary
    print("\n✏️ Test 4: Edit Tool Analysis")
    claude_response_edit = """
    I've updated the component to use proper TypeScript interfaces instead of any types.
    The changes include defining a proper Props interface and adding JSDoc comments
    for better documentation. This will improve type safety and developer experience.
    """
    
    result = _extract_tool_result(
        tool_name="Edit",
        tool_inputs={"file_path": "src/components/Header.tsx"},
        claude_response=claude_response_edit
    )
    print(f"✅ Edit result: {result}")
    
    # Test 5: Generic tool fallback
    print("\n🔧 Test 5: Generic Tool Fallback")
    claude_response_generic = """
    After analyzing the codebase structure, I found several areas that need improvement.
    The main issues are related to component complexity and TypeScript usage patterns.
    """
    
    result = _extract_tool_result(
        tool_name="CustomTool",
        tool_inputs={"param": "value"},
        claude_response=claude_response_generic
    )
    print(f"✅ Generic result: {result}")
    
    print("\n🎯 Summary")
    print("=" * 50)
    print("✅ Enhanced tool result extraction captures Claude's contextual analysis")
    print("✅ Real insights instead of raw tool outputs")
    print("✅ Meaningful data for LangSmith tracing")
    print("✅ All tool types supported with intelligent extraction")

if __name__ == "__main__":
    test_enhanced_extraction()
