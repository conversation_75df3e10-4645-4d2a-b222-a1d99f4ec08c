import logging
import os
from typing import Any, Dict, Optional

from data import client
from workflows.code.prompts import get_issue_prompt
from workflows.code.states import AgentState

from sandbox.modal_sandbox import (
    create_sandbox,
    cleanup_sandbox,
    run_claude,
    ClaudeSession,
)

from langgraph.graph import END, START, StateGraph
from langchain_core.runnables.config import RunnableConfig

# Lang<PERSON>mith tracing imports
try:
    from langsmith import get_current_run_tree
    LANGSMITH_AVAILABLE = True
except ImportError:
    LANGSMITH_AVAILABLE = False
    get_current_run_tree = None

logger = logging.getLogger(__name__)


class CodeGraph:
    """Graph for main coding workflow."""

    # =================================== HELPER METHODS ====================================

    def _get_parent_run(self) -> Optional[Any]:
        """Get current <PERSON><PERSON><PERSON> run context if available."""
        if not LANGSMITH_AVAILABLE:
            return None
        try:
            return get_current_run_tree()
        except Exception:
            return None

    async def _run_claude(
        self,
        sandbox: Any,
        prompt: str,
        claude_options: Optional[Dict[str, Any]] = None,
        timeout: int = 3600,
        max_retries: int = 2,
    ) -> Any:
        """Run Claude Code in the specified sandbox with retry mechanism.

        Args:
            sandbox: Sandbox instance
            prompt: Prompt for Claude
            claude_options: Options for Claude Code
            timeout: Timeout for execution
            max_retries: Maximum number of retry attempts

        Returns:
            ClaudeSession result
        """
        logger.info("🌊 Using Modal execution...")
        try:
            parent_run = self._get_parent_run()

            # Pass max_retries and parent_run to the run_claude function
            session = await run_claude(
                sandbox=sandbox,
                prompt=prompt,
                claude_options=claude_options,
                timeout=timeout,
                max_retries=max_retries,
                enable_tracing=True,
                parent_run=parent_run,
            )

            logger.info(
                f"✅ Claude execution completed with {len(session.outputs)} outputs"
            )
            return session

        except Exception as e:
            logger.error(f"💥 Error in Claude execution: {e}")
            # Create a minimal session to maintain interface
            session = ClaudeSession(
                session_id=f"modal-code-{id(sandbox)}-{timeout}", prompt=prompt
            )
            session.finalize(success=False, error=str(e))
            return session

    def _parse_issue_result(self, session) -> Optional[str]:
        """Parse Claude's issue result from session output to extract PR URL."""
        import json
        import re

        # Look for <result> JSON block in session outputs
        for output in session.outputs:
            if hasattr(output, "content"):
                content = str(output.content)
                # Find JSON between <result> tags
                match = re.search(r"<result>\s*({.*?})\s*</result>", content, re.DOTALL)
                if match:
                    try:
                        result = json.loads(match.group(1))
                        return result.get("pr_url")
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse JSON result for issue: {e}")

        # Fallback if no structured result found
        return None

    # =================================== NODES ====================================

    async def create_sandbox_node(
        self, state: AgentState, config: RunnableConfig
    ) -> Dict[str, Any]:
        """Create sandbox and initialize repository."""
        logger.info(f"🚀 Creating sandbox for code agent...")

        try:
            repo_id = state.get("repository_id", os.getenv("DEFAULT_REPO_ID"))

            if not repo_id:
                raise ValueError(
                    "No repository_id provided and DEFAULT_REPO_ID not set"
                )

            logger.info(f"📍 Repository ID: {repo_id}")

            parent_run = self._get_parent_run()

            # create sandbox with snapshot first
            try:
                sandbox = await create_sandbox(
                    repo_id=repo_id,
                    timeout=3600,
                    use_snapshot=True,
                    parent_run=parent_run,  # Pass parent run for tracing
                )
            except Exception as snapshot_e:
                logger.warning(
                    f"⚠️ Failed to create sandbox with snapshot: {snapshot_e}"
                )
                logger.info("🔄 Retrying without snapshot...")
                try:
                    # Fallback: create sandbox without snapshot
                    sandbox = await create_sandbox(
                        repo_id=repo_id,
                        timeout=3600,
                        use_snapshot=False,
                        parent_run=parent_run,  # Pass parent run for tracing
                    )
                    logger.info("✅ Created sandbox without snapshot")
                except Exception as no_snapshot_e:
                    logger.error(
                        f"❌ Failed to create sandbox without snapshot: {no_snapshot_e}"
                    )
                    raise Exception(
                        f"All sandbox creation methods failed. Snapshot error: {snapshot_e}. No-snapshot error: {no_snapshot_e}"
                    )

            return {
                "sandbox": sandbox,
                "repository_id": repo_id,
            }

        except Exception as e:
            logger.error(f"❌ Failed to create sandbox: {e}")
            return {
                "sandbox": None,
                "error": f"Failed to create sandbox: {str(e)}",
            }

    async def issue_node(
        self, state: AgentState, config: RunnableConfig
    ) -> Dict[str, Any]:
        """Investigate and complete the issue."""
        logger.info("📊 Starting ISSUE phase...")

        try:
            sandbox = state["sandbox"]
            issue_id = state.get("issue_id", "")

            if not issue_id:
                return {"error": "No issue to investigate"}

            issue = client.get_issue_by_id(issue_id)

            # Get issue prompt
            prompt = get_issue_prompt(issue)

            # Get retry configuration from state, default to 2
            max_retries = state.get("max_retries", 2)
            logger.info(f"🔄 Configured for max {max_retries} retries")

            # Run Claude Code for issue
            session = await self._run_claude(
                sandbox=sandbox,
                prompt=prompt,
                claude_options=state.get("claude_options", {}),
                timeout=3600,
                max_retries=max_retries,
            )

            if not session.success:
                logger.error(f"❌ Issue failed: {session.error}")
                return {"error": f"Issue failed: {session.error}"}

            logger.info("✅ Issue completed successfully")
            return {"pr_url": self._parse_issue_result(session)}

        except Exception as e:
            logger.error(f"❌ Issue phase error: {e}")
            return {"error": f"Issue phase error: {str(e)}"}

    async def cleanup_sandbox_node(
        self, state: AgentState, config: RunnableConfig
    ) -> Dict[str, Any]:
        """Clean up sandbox (E2B, Daytona, or Modal)."""
        logger.info("🧹 Cleaning up sandbox...")

        sandbox = state.get("sandbox")

        if sandbox:
            try:
                parent_run = self._get_parent_run()
                cleanup_run = None
                if parent_run:
                    try:
                        cleanup_run = parent_run.create_child(
                            name="🧹 Sandbox Cleanup",
                            run_type="tool",
                            inputs={"sandbox_type": "modal"}
                        )
                        cleanup_run.post()
                    except Exception:
                        cleanup_run = None

                await cleanup_sandbox(sandbox)
                logger.info("✅ sandbox cleaned up successfully")

                if cleanup_run:
                    try:
                        cleanup_run.end(outputs={"status": "success"})
                        cleanup_run.patch()
                    except:
                        pass

            except Exception as e:
                logger.warning(f"⚠️ Error during sandbox cleanup: {e}")
                if cleanup_run:
                    try:
                        cleanup_run.end(outputs={"status": "error", "error": str(e)})
                        cleanup_run.patch()
                    except:
                        pass

        return {"sandbox": None, "sandbox_run_id": None}

    # =================================== COMPILE ====================================

    def compile(self):
        """Compile the graph."""

        builder = StateGraph(AgentState)

        # Add core nodes
        builder.add_node("create_sandbox", self.create_sandbox_node)
        builder.add_node("issue", self.issue_node)
        builder.add_node("cleanup", self.cleanup_sandbox_node)

        # Define the flow
        builder.add_edge(START, "create_sandbox")
        builder.add_edge("create_sandbox", "issue")
        builder.add_edge("issue", "cleanup")
        builder.add_edge("cleanup", END)

        return builder.compile()


graph = CodeGraph().compile()
