import logging
import os
from typing import Any, Dict, Optional

from workflows.scan.states import (
    Aggregate<PERSON><PERSON>ult,
    AnalysisResult,
    AgentState,
)
from workflows.scan.prompts import (
    get_analysis_prompt,
    get_aggregation_prompt,
    get_init_prompt,
)

from sandbox.modal_sandbox import (
    create_sandbox,
    cleanup_sandbox,
    run_claude,
    ClaudeSession,
)

from workflows.scan.states import MetricType
from langgraph.graph import END, START, StateGraph
from langchain_core.runnables.config import RunnableConfig

# LangSmith tracing imports
try:
    from langsmith import get_current_run_tree
    LANGSMITH_AVAILABLE = True
except ImportError:
    LANGSMITH_AVAILABLE = False
    get_current_run_tree = None

logger = logging.getLogger(__name__)


class ScanGraph:
    """Graph for codebase scanning and quality analysis."""

    # =================================== HELPER METHODS ====================================

    def _get_parent_run(self) -> Optional[Any]:
        """Get current Lang<PERSON><PERSON> run context if available."""
        if not LANGSMITH_AVAILABLE:
            return None
        try:
            return get_current_run_tree()
        except Exception:
            return None

    async def _run_claude(
        self,
        sandbox: Any,
        prompt: str,
        claude_options: Optional[Dict[str, Any]] = None,
        timeout: int = 3600,
    ) -> Any:
        """Run Claude Code in the specified sandbox.

        Args:
            sandbox: Sandbox instance
            prompt: Prompt for Claude
            claude_options: Options for Claude Code
            timeout: Timeout for execution

        Returns:
            ClaudeSession result
        """
        logger.info("🌊 Using Modal execution...")
        try:
            parent_run = self._get_parent_run()

            # Simply await the run_claude function - it returns a ClaudeSession
            session = await run_claude(
                sandbox=sandbox,
                prompt=prompt,
                claude_options=claude_options,
                timeout=timeout,
                enable_tracing=True,
                parent_run=parent_run,
            )

            logger.info(
                f"✅ Claude execution completed with {len(session.outputs)} outputs"
            )
            return session

        except Exception as e:
            logger.error(f"💥 Error in Claude execution: {e}")
            # Create a minimal session to maintain interface
            session = ClaudeSession(
                session_id=f"modal-scanner-{id(sandbox)}-{timeout}", prompt=prompt
            )
            session.finalize(success=False, error=str(e))
            return session

    # =================================== NODES ====================================

    async def create_sandbox_node(
        self, state: AgentState, config: RunnableConfig
    ) -> Dict[str, Any]:
        """Create sandbox and initialize repository."""
        logger.info(f"🚀 Creating sandbox for Claude-Scanner session...")

        try:
            repo_id = state.get("repository_id", os.getenv("DEFAULT_REPO_ID"))

            if not repo_id:
                raise ValueError(
                    "No repository_id provided and DEFAULT_REPO_ID not set"
                )

            logger.info(f"📍 Repository ID: {repo_id}")

            parent_run = self._get_parent_run()

            # Try to create sandbox with snapshot first
            try:
                sandbox = await create_sandbox(
                    repo_id=repo_id,
                    timeout=3600,
                    use_snapshot=True,
                    parent_run=parent_run,  # Pass parent run for tracing
                )
            except Exception as snapshot_e:
                logger.warning(
                    f"⚠️ Failed to create sandbox with snapshot: {snapshot_e}"
                )
                logger.info("🔄 Retrying without snapshot...")
                try:
                    # Fallback: create sandbox without snapshot
                    sandbox = await create_sandbox(
                        repo_id=repo_id,
                        timeout=3600,
                        use_snapshot=False,
                        parent_run=parent_run,  # Pass parent run for tracing
                    )
                    logger.info("✅ Created sandbox without snapshot")
                except Exception as no_snapshot_e:
                    logger.error(
                        f"❌ Failed to create sandbox without snapshot: {no_snapshot_e}"
                    )
                    raise Exception(
                        f"All sandbox creation methods failed. Snapshot error: {snapshot_e}. No-snapshot error: {no_snapshot_e}"
                    )

            return {
                "sandbox": sandbox,
                "repository_id": repo_id,
            }

        except Exception as e:
            logger.error(f"❌ Failed to create sandbox: {e}")
            return {
                "sandbox": None,
                "error": f"Failed to create sandbox: {str(e)}",
            }

    def analysis_node(self, metric_type: MetricType):
        """Create a metric analysis node for the given metric type."""

        async def _analysis_node(
            state: AgentState, config: RunnableConfig
        ) -> Dict[str, Any]:
            logger.info(f"🔍 Starting {metric_type.value.upper()} analysis...")

            try:
                sandbox = state["sandbox"]

                # Get prompt for this specific metric
                prompt = get_analysis_prompt(metric_type.value)

                # Run Claude Code with parent run context
                session = await self._run_claude(
                    sandbox=sandbox,
                    prompt=prompt,
                    claude_options=state.get("claude_options", {}),
                    timeout=3600,
                )

                if not session.success:
                    logger.error(
                        f"❌ {metric_type.value} analysis failed: {session.error}"
                    )
                    return {
                        "error": f"{metric_type.value} analysis failed: {session.error}"
                    }

                # Parse Claude's response to extract structured result
                # This assumes Claude returns a <result> JSON block as per prompts
                analysis_result = self._parse_analysis_result(session, metric_type)

                logger.info(
                    f"✅ {metric_type.value.upper()} analysis completed successfully"
                )
                return {"analysis_results": [analysis_result]}

            except Exception as e:
                logger.error(f"❌ {metric_type.value} analysis error: {e}")
                return {"error": f"{metric_type.value} analysis error: {str(e)}"}

        return _analysis_node

    def _parse_analysis_result(
        self, session, metric_type: MetricType
    ) -> AnalysisResult:
        """Parse Claude's analysis result from session output."""
        import json
        import re

        # Look for <result> JSON block in session outputs
        for output in session.outputs:
            if hasattr(output, "content"):
                content = str(output.content)
                # Find JSON between <result> tags
                match = re.search(r"<result>\s*({.*?})\s*</result>", content, re.DOTALL)
                if match:
                    try:
                        result = json.loads(match.group(1))
                        return AnalysisResult(
                            metric=metric_type,
                            score=result.get("score", 0),
                            summary=result.get("summary", ""),
                            issues=result.get("issues", []),
                        )
                    except json.JSONDecodeError as e:
                        logger.warning(
                            f"Failed to parse JSON result for {metric_type.value}: {e}"
                        )

        # Fallback if no structured result found
        return AnalysisResult(
            metric=metric_type,
            score=0,
            summary="",
            issues=[],
        )

    async def init_node(
        self, state: AgentState, config: RunnableConfig
    ) -> Dict[str, Any]:
        """Initialize the CLAUDE.md file."""
        logger.info("📊 Starting INIT phase...")

        try:
            sandbox = state["sandbox"]

            # Get aggregation prompt
            prompt = get_init_prompt()

            # Run Claude Code for initialization with parent run context
            session = await self._run_claude(
                sandbox=sandbox,
                prompt=prompt,
                claude_options=state.get("claude_options", {}),
                timeout=3600,
            )

            if not session.success:
                logger.error(f"❌ INIT failed: {session.error}")
                return {"error": f"INIT failed: {session.error}"}

            logger.info("✅ INIT phase completed successfully")
            return {"init_result": "CLAUDE.md file created successfully"}

        except Exception as e:
            logger.error(f"❌ Aggregate phase error: {e}")
            return {"error": f"Aggregate phase error: {str(e)}"}

    async def aggregate_node(
        self, state: AgentState, config: RunnableConfig
    ) -> Dict[str, Any]:
        """Aggregate all metric analysis results and create prioritized task groups."""
        logger.info("📊 Starting AGGREGATE phase...")

        try:
            sandbox = state["sandbox"]
            analysis_results = state.get("analysis_results", [])

            if not analysis_results:
                return {"error": "No analysis results to aggregate"}

            # Get aggregation prompt
            prompt = get_aggregation_prompt(analysis_results)

            # Run Claude Code for aggregation with parent run context
            session = await self._run_claude(
                sandbox=sandbox,
                prompt=prompt,
                claude_options=state.get("claude_options", {}),
                timeout=3600,
            )

            if not session.success:
                logger.error(f"❌ Aggregation failed: {session.error}")
                return {"error": f"Aggregation failed: {session.error}"}

            # Parse aggregated result
            aggregate_result = self._parse_aggregate_result(session)

            logger.info("✅ AGGREGATE phase completed successfully")
            return {"aggregate_result": aggregate_result}

        except Exception as e:
            logger.error(f"❌ Aggregate phase error: {e}")
            return {"error": f"Aggregate phase error: {str(e)}"}

    def _parse_aggregate_result(self, session) -> AggregateResult:
        """Parse Claude's aggregation result from session output."""
        import json
        import re

        # Look for <result> JSON block in session outputs
        for output in session.outputs:
            if hasattr(output, "content"):
                content = str(output.content)
                # Find JSON between <result> tags
                match = re.search(r"<result>\s*({.*?})\s*</result>", content, re.DOTALL)
                if match:
                    try:
                        result = json.loads(match.group(1))
                        return AggregateResult(
                            summary=result.get("summary", ""),
                            issues=result.get("issues", []),
                        )
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse JSON aggregate result: {e}")

        # Fallback if no structured result found
        return AggregateResult(summary="", issues=[])

    async def cleanup_sandbox_node(
        self, state: AgentState, config: RunnableConfig
    ) -> Dict[str, Any]:
        """Clean up sandbox (E2B, Daytona, or Modal)."""
        logger.info("🧹 Cleaning up sandbox...")

        sandbox = state.get("sandbox")

        if sandbox:
            try:
                parent_run = self._get_parent_run()
                cleanup_run = None
                if parent_run:
                    try:
                        cleanup_run = parent_run.create_child(
                            name="🧹 Sandbox Cleanup",
                            run_type="tool",
                            inputs={"sandbox_type": "modal"}
                        )
                        cleanup_run.post()
                    except Exception:
                        cleanup_run = None

                await cleanup_sandbox(sandbox)
                logger.info("✅ sandbox cleaned up successfully")

                if cleanup_run:
                    try:
                        cleanup_run.end(outputs={"status": "success"})
                        cleanup_run.patch()
                    except:
                        pass

            except Exception as e:
                logger.warning(f"⚠️ Error during sandbox cleanup: {e}")
                if cleanup_run:
                    try:
                        cleanup_run.end(outputs={"status": "error", "error": str(e)})
                        cleanup_run.patch()
                    except:
                        pass

        return {"sandbox": None, "sandbox_run_id": None}

    # =================================== COMPILE ====================================

    def compile(self):
        """Compile the graph."""

        builder = StateGraph(AgentState)

        # Add core nodes
        builder.add_node("create_sandbox", self.create_sandbox_node)
        # builder.add_node("init", self.init_node)
        builder.add_node("aggregate", self.aggregate_node)
        builder.add_node("cleanup", self.cleanup_sandbox_node)

        # Add individual metric analysis nodes
        builder.add_node("clarity", self.analysis_node(MetricType.CLARITY))
        # builder.add_node("test_coverage", self.analysis_node(MetricType.TEST_COVERAGE))
        # builder.add_node("modularity", self.analysis_node(MetricType.MODULARITY))
        # builder.add_node("security", self.analysis_node(MetricType.SECURITY))
        # builder.add_node("performance", self.analysis_node(MetricType.PERFORMANCE))
        # builder.add_node(
        #     "resilience", self.analysis_node(MetricType.RESILIENCE)
        # )

        # Define the flow
        builder.add_edge(START, "create_sandbox")
        builder.add_edge("create_sandbox", "clarity")

        # From init, go to all metric analysis nodes in parallel
        # builder.add_edge("init", "clarity")
        # builder.add_edge("init", "test_coverage")
        # builder.add_edge("init", "modularity")
        # builder.add_edge("init", "security")
        # builder.add_edge("init", "performance")
        # builder.add_edge("init", "resilience")

        # Each metric analysis node goes directly to aggregate
        builder.add_edge("clarity", "aggregate")
        # builder.add_edge("test_coverage", "aggregate")
        # builder.add_edge("modularity", "aggregate")
        # builder.add_edge("security", "aggregate")
        # builder.add_edge("performance", "aggregate")
        # builder.add_edge("resilience", "aggregate")

        # From aggregate, go to cleanup
        builder.add_edge("aggregate", "cleanup")
        builder.add_edge("cleanup", END)

        return builder.compile()


graph = ScanGraph().compile()
