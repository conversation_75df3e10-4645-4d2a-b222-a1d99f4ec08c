from jinja2 import Template


INIT_PROMPT = """
Create a comprehensive codebase navigation map (CLAUDE.md) for agentic coding assistants. This map serves as the primary reference for understanding project structure and patterns.

1. **Check for existing documentation**
   - Look for CLAUDE.md, AGENTS.md, or similar navigation files
   - If CLAUDE.md exists, exit with a message that it already exists
   - Preserve any custom notes or sections added manually

2. **Discover project structure**
   - Framework type and version (Next.js, React, Vue, etc.)
   - Project architecture (App Router vs Pages Router for Next.js)
   - Directory organization and naming patterns
   - Key configuration files (package.json, tsconfig.json, next.config.js)
   - Build and deployment setup

3. **Map all routes and endpoints**
   - Page routes with their file locations
   - API endpoints with supported methods
   - Dynamic routes and parameters
   - Middleware and special files (layout, error, loading)
   - Authentication and protected routes

4. **Document component architecture**
   - Component organization strategy
   - Design system and UI components location
   - State management solution and patterns
   - Custom hooks and utilities
   - Shared/common code structure

5. **Identify key patterns**
   - Data fetching approach (SSR, SSG, ISR, client-side)
   - Error handling and loading states
   - Testing setup and coverage
   - Code standards and linting rules
   - Performance optimizations

6. **Create CLAUDE.md with this structure**:
   - Project metadata (type, version, last updated)
   - Quick start commands
   - Complete directory tree with descriptions
   - Routes table (all pages and APIs)
   - Component hierarchy
   - Common development tasks with examples
   - Architecture decisions and patterns
   - Debugging tips and useful commands

IMPORTANT: Be exhaustive - map ALL routes and components, not samples. The goal is a single source of truth that enables any agent to quickly navigate and understand the codebase without additional discovery.
"""

ANALYSIS_PROMPT = Template(
    """
<base_instructions>
You are a staff software engineer, specialized in Vercel, Next.js and Turborepo. Your role is to perform a technical deep dive into the codebase and document issues found into a list with suggested fixes for Claude to implement. You will be given a metric to focus on, and you will need to follow the instructions below to perform the analysis.

**Note**: The CLAUDE.md file has already been created by the initial analysis step and contains comprehensive project structure, routes, components, and patterns information. Use this as your primary reference.

As you navigate the codebase, keep track of findings with precise references:
   - Always note exact file paths relative to project root
   - Include line numbers when referencing specific code
   - Group related issues that share the same root cause
   - Use Context7 tool to consult the Next.js docs for best practices when needed

Your final output should be a JSON object within <result> tags with the following structure:
<result>
{
  "score": "number - assign a metric score from 1-10",
  "summary": "string - one paragraph executive summary of findings",
  "issues": [
    {
      "locations": ["array of strings - e.g., 'app/dashboard/page.tsx:45-52', 'components/Header.tsx:12'"],
      "description": "string - specific technical description of the issue",
      "severity": "critical|high|medium|low",
      "suggested_fix": "string - actionable fix with code examples if applicable"
    }
  ],
}
</result>
</base_instructions>

<metric_instructions>
{{ metric_instructions }}
</metric_instructions>

IMPORTANT:
- Be exhaustive in your search - check all relevant files
- Reference official Vercel/Next.js documentation with the Context7 tool
- Provide concrete, implementable fixes
- Only present the top 5 low hanging fruits: important issues that are also straightforward for Claude to implement.
"""
)

# =========================== METRIC-SPECIFIC PROMPTS ===========================

TEST_COVERAGE_PROMPT = """
## TEST COVERAGE ANALYSIS

Analyze test coverage comprehensively for Next.js applications to identify gaps that could lead to production issues.

**Prerequisites**: Review CLAUDE.md for project structure, routes, and component locations.

### Quick Assessment:
```bash
# Check test configuration and run coverage
cat package.json | grep -A5 -B5 "test"
npm test -- --coverage || yarn test --coverage || npx vitest run --coverage

# Count existing tests
find . -name "*.test.*" -o -name "*.spec.*" | wc -l
```

### Analysis Focus:
1. **Configuration Assessment**:
   - Check jest.config.js/vitest.config.js for coverage thresholds
   - Verify test environment setup for Next.js (jsdom, node)
   - Check for proper module aliases matching tsconfig paths

2. **Component Testing** (use CLAUDE.md component list):
   - Check each component for corresponding test file
   - Look for untested custom hooks
   - Verify critical UI components have tests

3. **API Route Testing** (use CLAUDE.md routes table):
   - Verify each endpoint has tests for its supported methods
   - Check error handling test coverage
   - Validate auth middleware testing

4. **Next.js Feature Testing**:
   - Server Components: Look for async components and data fetching
   - Client Components: Check for "use client" directive coverage
   - Middleware: Verify middleware.ts has tests
   - Loading/Error states: Check loading.tsx and error.tsx files

### Issue Detection Patterns:
- Components without tests: No corresponding .test.tsx file
- API routes without tests: No test coverage for route handlers
- Untested edge cases: No error boundary tests, no loading state tests
- Missing integration tests: No tests that span multiple components
- Low coverage areas: Coverage report shows <70% for critical paths

### Task Generation:
For each untested area, create tasks specifying:
- Exact file to test with full path
- Test scenarios needed (happy path, error cases, edge cases)
- Specific Next.js features to test (SSR, hydration, router behavior)
- Example test structure for the specific component type

### Scoring Guidelines:
- **10**: >95% coverage, all critical paths tested, E2E tests present
- **8-9**: 85-95% coverage, most Next.js features tested, good E2E coverage
- **6-7**: 70-85% coverage, core functionality tested, some E2E tests
- **4-5**: 50-70% coverage, significant gaps in API/component testing
- **1-3**: <50% coverage, critical features untested, no E2E tests
"""

CLARITY_PROMPT = """
## CLARITY ANALYSIS

Evaluate code readability, documentation quality, and Next.js architectural clarity.

**Prerequisites**: Review CLAUDE.md for project architecture and patterns.

### Quick Assessment:
```bash
# Check TypeScript strictness and find complex files
cat tsconfig.json | grep -E "strict|noImplicit"
find . -name "*.tsx" -o -name "*.ts" | xargs wc -l | sort -rn | head -20
```

### Analysis Focus:
1. **Component Complexity** (use CLAUDE.md component hierarchy):
   - Identify components > 200 lines
   - Check for prop spreading anti-patterns
   - Look for components with > 5 props without interfaces

2. **TypeScript Quality**:
   - Check for `any` types: `grep -r ": any" --include="*.{ts,tsx}" .`
   - Look for missing return types on functions
   - Verify interfaces are properly defined for props
   - Check for proper generic usage in components

3. **File Organization** (compare with CLAUDE.md structure):
   - Verify consistent file naming
   - Check for barrel exports causing circular dependencies
   - Ensure feature-based folder structure
   - Look for co-located tests and styles

4. **Documentation Gaps**:
   - Components without prop documentation
   - API routes without endpoint documentation
   - Complex business logic without explanatory comments
   - Missing architecture decision records (ADRs)

### Issue Detection Patterns:
- Functions > 50 lines without clear separation
- Components with > 5 props without proper interface
- Files with > 300 lines indicating poor separation
- Inconsistent naming patterns across similar components
- Magic numbers/strings without constants
- Complex conditionals without explanatory comments

### Task Generation:
For each clarity issue:
- Specify exact refactoring needed
- Provide before/after code examples
- Include TypeScript interface definitions needed
- Suggest component composition patterns
- Reference Next.js documentation for patterns

### Scoring Guidelines:
- **10**: Self-documenting code, comprehensive types, clear architecture
- **8-9**: Well-organized, minor documentation gaps, good TypeScript usage
- **6-7**: Mostly clear, some complex areas, adequate documentation
- **4-5**: Confusing structure, poor naming, minimal documentation
- **1-3**: Very difficult to understand, no clear patterns, poor organization
"""

MODULARITY_PROMPT = """
## MODULARITY ANALYSIS

Analyze coupling, cohesion, and Next.js architectural boundaries to assess change isolation.

**Prerequisites**: Review CLAUDE.md for component hierarchy and architecture patterns.

### Quick Assessment:
```bash
# Check for circular dependencies and bundle analysis
npx madge --circular --warning src/
ANALYZE=true npm run build
```

### Analysis Focus:
1. **Component Coupling Analysis** (use CLAUDE.md component map):
   - Identify components importing from multiple feature areas
   - Check for prop drilling patterns
   - Look for tightly coupled components

2. **Module Boundaries**:
   - Check if features are self-contained
   - Look for cross-feature imports
   - Verify API routes don't import from UI components
   - Ensure server/client code separation

3. **Shared Code Organization**:
   - Identify truly shared utilities vs. misplaced business logic
   - Check for feature-specific code in shared folders
   - Verify hooks are properly abstracted

4. **State Management Patterns**:
   - Look for global state overuse
   - Check for proper React Context boundaries
   - Identify components with too many state dependencies

### Issue Detection Patterns:
- Circular dependencies between modules
- Components importing from more than 3 different feature areas
- Business logic in UI components
- Shared utilities with feature-specific code
- Direct file imports instead of barrel exports
- Tightly coupled API routes and UI components

### Task Generation:
For each modularity issue:
- Identify the exact coupling points
- Suggest extraction patterns (hooks, utilities, components)
- Provide dependency injection solutions
- Recommend composition over inheritance
- Suggest proper module boundaries

### Scoring Guidelines:
- **10**: Clear boundaries, no circular deps, proper separation of concerns
- **8-9**: Well-modularized, minor coupling issues, good component isolation
- **6-7**: Decent structure, some tight coupling, boundaries mostly clear
- **4-5**: Significant coupling, unclear boundaries, hard to change safely
- **1-3**: Highly coupled, no clear architecture, changes affect many files
"""

SECURITY_PROMPT = """
## SECURITY ANALYSIS

Perform comprehensive security analysis for Next.js applications to identify vulnerabilities and insecure practices.

**Prerequisites**: Review CLAUDE.md for API routes, authentication patterns, and middleware setup.

### Quick Assessment:
```bash
# Check for vulnerabilities and dangerous patterns
npm audit --audit-level=moderate
grep -r "dangerouslySetInnerHTML\\|eval\\|innerHTML" --include="*.{ts,tsx}" .
```
# Run Semgrep security scan
semgrep --config=auto --json > security-report.json
```

### Analysis Focus:
1. **API Route Security** (use CLAUDE.md routes table):
   - Check each API route for authentication
   - Verify input validation on all endpoints
   - Look for rate limiting implementation

2. **Authentication & Authorization**:
   - Check middleware.ts for auth logic
   - Verify protected routes have guards
   - Look for JWT validation
   - Check session management

3. **Input Validation**:
   - API routes without input validation
   - Forms without client/server validation
   - SQL injection risks in database queries
   - XSS vulnerabilities in user content

4. **Configuration Security**:
   - Exposed API keys in client code
   - Missing security headers
   - Insecure cookies configuration
   - CORS misconfiguration

### Issue Detection Patterns:
- API routes without authentication checks
- Direct database queries without parameterization
- Missing CSRF protection
- Unvalidated user input in API routes
- Client-side secrets or API keys
- Missing rate limiting
- Insecure direct object references
- Missing security headers (CSP, HSTS, etc.)

### Task Generation:
For each security issue:
- Specify the exact vulnerability type
- Provide OWASP reference if applicable
- Include secure code example
- Suggest Next.js-specific security measures
- Reference middleware implementation for auth

### Scoring Guidelines:
- **10**: Comprehensive security, all OWASP top 10 addressed, proper auth
- **8-9**: Good security posture, minor improvements needed
- **6-7**: Basic security implemented, some vulnerabilities present
- **4-5**: Significant security issues, missing critical protections
- **1-3**: Critical vulnerabilities, immediate security risks
"""

PERFORMANCE_PROMPT = """
## PERFORMANCE ANALYSIS
                              
Identify Next.js performance bottlenecks, inefficiencies, and optimization opportunities.

**Prerequisites**: Review CLAUDE.md for data fetching patterns and component architecture.

### Quick Assessment:
```bash
# Build and check bundle size
ANALYZE=true npm run build
grep -r "Image" --include="*.{tsx,jsx}" . | grep -c "next/image"
```

### Analysis Focus:
1. **Bundle Size Analysis** (reference CLAUDE.md routes):
   - Check page-specific bundle sizes
   - Identify large components
   - Look for code splitting opportunities

2. **Rendering Performance**:
   - Check for unnecessary client components ("use client")
   - Look for missing React.memo on expensive components
   - Find components with excessive re-renders
   - Verify proper key usage in lists

3. **Data Fetching Optimization** (use CLAUDE.md patterns):
   - Parallel vs sequential data fetching
   - Missing cache headers
   - Over-fetching in API routes
   - Missing ISR/SSG where applicable

4. **Asset Optimization**:
   - Unoptimized images (not using next/image)
   - Missing font optimization
   - Large third-party scripts
   - Missing resource hints (preload, prefetch)

### Issue Detection Patterns:
- Images without width/height causing layout shift
- Missing loading="lazy" on below-fold content
- Synchronous data fetching blocking render
- Large JavaScript bundles (>200KB per route)
- Missing code splitting opportunities
- Unoptimized fonts loading
- Client-side data fetching that could be server-side
- Missing caching strategies

### Task Generation:
For each performance issue:
- Quantify the impact (bundle size, load time)
- Provide specific optimization technique
- Include before/after code examples
- Reference Next.js optimization docs

### Scoring Guidelines:
- **10**: Highly optimized, <3s FCP, all Core Web Vitals green
- **8-9**: Good performance, minor optimizations available
- **6-7**: Acceptable performance, noticeable improvement opportunities  
- **4-5**: Performance issues impacting UX, needs optimization
- **1-3**: Severe performance problems, very slow load times
"""

RESILIENCE_PROMPT = """
## RESILIENCE ANALYSIS

Evaluate error handling, recovery mechanisms, and production stability patterns for Next.js applications.

**Prerequisites**: Review CLAUDE.md for error handling patterns and loading states.

### Quick Assessment:
```bash
# Check for error boundaries and error pages
ls -la app/error.tsx app/global-error.tsx pages/_error.tsx 2>/dev/null
grep -r "ErrorBoundary\\|componentDidCatch" --include="*.{tsx,jsx}" . | wc -l
```

### Analysis Focus:
1. **Error Boundary Coverage** (use CLAUDE.md component list):
   - Map components that need error boundaries
   - Check for proper error boundary placement
   - Verify fallback UI implementation

2. **API Error Handling** (use CLAUDE.md API routes):
   - Check all route handlers for try-catch blocks
   - Verify consistent error response format
   - Look for proper status codes
   - Check for error logging

3. **Client-Side Resilience**:
   - Missing fallback UI for errors
   - Unhandled promise rejections
   - Network request error handling
   - Retry logic for failed requests

4. **Data Loading States**:
   - Missing loading indicators
   - No skeleton screens
   - Unhandled empty states
   - Missing offline support

### Issue Detection Patterns:
- Components that can throw without error boundaries
- API routes with no error handling
- Missing 404/500 error pages
- No loading states for async operations
- Console.error instead of proper error reporting
- Missing network failure handling
- No graceful degradation
- Unhandled edge cases in data processing

### Task Generation:
For each resilience issue:
- Identify the failure mode
- Specify error boundary or try-catch needed
- Provide fallback UI examples
- Suggest retry strategies
- Include logging/monitoring setup
- Reference Next.js error handling docs

### Scoring Guidelines:
- **10**: Comprehensive error handling, graceful degradation, monitoring
- **8-9**: Good resilience, minor gaps in error scenarios
- **6-7**: Basic error handling present, some unhandled cases
- **4-5**: Significant gaps, errors crash components/pages
- **1-3**: Poor error handling, unstable in production
"""

# =========================== AGGREGATION PROMPT ===========================

AGGREGATE_PROMPT = Template(
    """You are a staff software engineer specialized in Vercel, Next.js and Turborepo. 
    
<analysis_results>
In your previous step, you conducted a systematic analysis of the codebase to identify deviations from Next.js best practices. You documented these findings as follows:
{{ analysis_results }}
</analysis_results>

Your task is to create a prioritized, actionable task list for the development team. Feel free to revisit files if needed.

1. Prioritize each issue using these criteria:
   - **Critical**: Crashes, data loss, security vulnerabilities, or complete feature breakage
   - **High**: User-facing performance issues (>1s impact), SEO problems, major DX blockers (>30min daily)
   - **Medium**: Minor performance issues (<1s), maintenance difficulties, DX friction (5-30min daily)
   - **Low**: Nice-to-have improvements, style preferences, documentation updates

2. Identify dependencies between fixes:
   - Which fixes block others
   - Which can be done in parallel
   - Optimal order to minimize disruption
   - Combine related issues as sub-issues of a single issue

3. Create the final report of the issues found with code snippets and mermaid diagrams. Format your output as a JSON object within <result> tags:
<result>
{
  "report": "string - a markdown report of the issues found with code snippets and mermaid diagrams",
  "issues": [
    {
      "id": "integer - unique identifier for the issue (auto-incremented)",
      "title": "string - clear, actionable title",
      "description": "string - what problem this solves. Include testing requirements.",
      "tags": ["array of strings - metric types, feature areas, etc."],
      "priority": "critical|high|medium|low",
      "dependencies_ids": ["array of issue IDs that must be completed first"],
      "sub_issues": [
        { 
          "id": "integer - unique identifier for the sub-issue (auto-incremented)",
          "title": "string - specific task",
          "description": "string - detailed implementation steps. Include files affected and acceptance criteria.",
        }
      ],
    }
  ]
}
</result>

IMPORTANT:
- Create phases that can be executed in parallel by different team members
- Each issue should be completable in one PR
- Include specific file paths from the analysis
- Make sub-issues concrete and implementable by junior developers
"""
)


# =========================== PROMPT GENERATORS ===========================


def get_analysis_prompt(metric_type: str) -> str:
    """Get system prompt for specific metric analysis."""
    prompts = {
        "test_coverage": TEST_COVERAGE_PROMPT,
        "clarity": CLARITY_PROMPT,
        "modularity": MODULARITY_PROMPT,
        "security": SECURITY_PROMPT,
        "performance": PERFORMANCE_PROMPT,
        "resilience": RESILIENCE_PROMPT,
    }

    metric_prompt = prompts.get(metric_type)
    if not metric_prompt:
        raise ValueError(f"Unknown metric type: {metric_type}")

    return ANALYSIS_PROMPT.render(metric_instructions=metric_prompt)


def get_aggregation_prompt(analysis_results: list[dict]) -> str:
    """Get system and user prompts for result aggregation."""
    prompt = AGGREGATE_PROMPT.render(analysis_results=analysis_results)
    return prompt


def get_init_prompt() -> str:
    """Get system prompt for repository initialization."""
    return INIT_PROMPT
