#!/usr/bin/env python3
"""
Simple test for Modal tracing functionality.
"""

import asyncio
import logging
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

async def test_modal_tracing():
    """Test Modal sandbox tracing."""
    print("🧪 Testing Modal Sandbox Tracing")
    print("=" * 50)
    
    try:
        # Import Modal sandbox functions
        from sandbox.modal_sandbox import check_tracing_config, run_claude_with_tracing, create_sandbox, cleanup_sandbox
        
        # Check tracing configuration
        config = check_tracing_config()
        print("📊 Tracing Configuration:")
        for key, value in config.items():
            status = "✅" if value else "❌"
            print(f"  {status} {key}: {value}")
        
        # Create a sandbox for testing
        print("\n🚀 Creating sandbox...")
        sandbox = await create_sandbox(
            repo_id="1",
            timeout=300,
            use_snapshot=True,
        )
        
        try:
            # Test a simple Claude command with tracing
            print("🤖 Running Claude with tracing...")
            session = await run_claude_with_tracing(
                sandbox=sandbox,
                prompt="What files are in the current directory? Just list them briefly.",
                timeout=30,
                enable_tracing=True,
            )
            
            print(f"✅ Claude session completed!")
            print(f"📊 Session ID: {session.session_id}")
            print(f"⏱️  Duration: {session.elapsed_time:.2f}s")
            print(f"📝 Outputs: {len(session.outputs)}")
            print(f"🎯 Tracing enabled: {session.trace_enabled}")
            print(f"🔗 Run tree: {'Yes' if session.run_tree else 'No'}")
            
            if session.trace_enabled and session.run_tree:
                print(f"🌳 Child runs created: {len(session.child_runs)}")
                print("🎉 LangSmith tracing is working!")
                return True
            else:
                print("⚠️  Tracing was not enabled for this session")
                return False
            
        finally:
            # Clean up sandbox
            await cleanup_sandbox(sandbox)
            print("🧹 Sandbox cleaned up")
        
    except Exception as e:
        print(f"❌ Modal tracing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔬 Simple LangSmith Tracing Test")
    print("=" * 60)
    
    # Check environment
    api_key = os.getenv("LANGSMITH_API_KEY")
    project = os.getenv("LANGSMITH_PROJECT")
    tracing = os.getenv("LANGSMITH_TRACING")
    
    print("🔍 Environment Check:")
    print(f"  API Key: {'✅ Set' if api_key else '❌ Missing'}")
    print(f"  Project: {project or '❌ Missing'}")
    print(f"  Tracing: {tracing or '❌ Missing'}")
    
    if not all([api_key, project, tracing]):
        print("❌ Missing required environment variables")
        return
    
    # Run test
    success = asyncio.run(test_modal_tracing())
    
    if success:
        print("\n🎉 Test passed! LangSmith tracing is working.")
        print(f"🌐 Check your traces at: {os.getenv('LANGSMITH_ENDPOINT')}")
        print(f"📊 Project: {os.getenv('LANGSMITH_PROJECT')}")
    else:
        print("\n❌ Test failed. Check the error messages above.")

if __name__ == "__main__":
    main()
