#!/usr/bin/env python3
"""
Test script to verify <PERSON><PERSON>rap<PERSON> + <PERSON><PERSON> tracing integration for scan workflow.
"""

import asyncio
import logging
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

async def test_scan_graph_tracing():
    """Test the scan graph with tracing integration."""
    logger.info("🧪 Testing Scan LangGraph + LangSmith tracing integration")
    
    try:
        from workflows.scan.graph import graph
        from workflows.scan.states import AgentState
        
        # Create a simple test state
        initial_state: AgentState = {
            "sandbox": None,
            "repository_id": "1",
            "analysis_results": [],
            "aggregate_result": None,
            "error": None,
        }
        
        logger.info("🚀 Starting scan graph execution...")
        
        # Run the scan graph to test tracing
        result = await graph.ainvoke(initial_state, {"configurable": {"thread_id": "scan-test-123"}})
        
        logger.info(f"✅ Scan graph execution completed")
        logger.info(f"📊 Result keys: {list(result.keys())}")
        
        if result.get("error"):
            logger.error(f"❌ Scan graph execution had error: {result['error']}")
        else:
            logger.info("✅ Scan graph execution successful")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    logger.info("🎯 Starting Scan LangGraph tracing integration test")
    
    # Check environment
    langsmith_key = os.getenv("LANGSMITH_API_KEY") or os.getenv("LANGCHAIN_API_KEY")
    langsmith_tracing = os.getenv("LANGSMITH_TRACING") or os.getenv("LANGCHAIN_TRACING_V2")
    langsmith_project = os.getenv("LANGSMITH_PROJECT") or os.getenv("LANGCHAIN_PROJECT")
    
    logger.info(f"🔑 LangSmith API Key: {'✅ Set' if langsmith_key else '❌ Not set'}")
    logger.info(f"🔍 LangSmith Tracing: {'✅ Enabled' if langsmith_tracing == 'true' else '❌ Disabled'}")
    logger.info(f"📁 LangSmith Project: {langsmith_project or '❌ Not set'}")
    
    if not (langsmith_key and langsmith_tracing == 'true'):
        logger.warning("⚠️ LangSmith not properly configured - tracing may not work")
    
    # Run the test
    success = asyncio.run(test_scan_graph_tracing())
    
    if success:
        logger.info("🎉 Scan test completed successfully!")
        logger.info("🔗 Check your LangSmith dashboard for the trace")
        logger.info("📊 You should see: create_sandbox, clarity, aggregate, cleanup as parent nodes")
        logger.info("🎯 With Claude execution nested under each analysis node")
    else:
        logger.error("💥 Scan test failed!")
    
    return success

if __name__ == "__main__":
    main()
