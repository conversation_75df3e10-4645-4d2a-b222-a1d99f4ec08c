#!/usr/bin/env python3
"""
Debug raw streaming events from Claude Code to understand tool result format.
"""

import asyncio
import logging
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

async def debug_raw_streaming():
    """Capture and analyze raw streaming events from Claude Code."""
    print("🔍 Debugging raw Claude Code streaming events...")
    
    try:
        from sandbox.modal_sandbox import create_sandbox
        
        # Create sandbox
        print("🚀 Creating sandbox...")
        sandbox = await create_sandbox(repo_id="1", timeout=300, use_snapshot=True)
        print("✅ Sandbox created")
        
        # Test simple command that should generate tool results
        print("\n⚡ Testing Claude Code with raw event capture...")
        
        # Build Claude command
        prompt = "List the files in the current directory using ls command"
        escaped_prompt = prompt.replace("'", "'\"'\"'")
        claude_command = "claude -p --output-format stream-json --verbose --allowedTools Edit,Write,MultiEdit,Read,Bash"
        full_command = f"cd /workspace && echo '{escaped_prompt}' | {claude_command}"
        
        print(f"📝 Command: {full_command}")
        
        # Execute and capture raw streaming
        print("\n🌊 Starting raw stream capture...")
        process = sandbox.exec("bash", "-c", full_command, timeout=60)
        
        raw_events = []
        event_count = 0
        
        # Read streaming output line by line
        for line in process.stdout:
            line = line.strip()
            if not line:
                continue
                
            event_count += 1
            print(f"\n📦 Raw Event #{event_count}:")
            print(f"   Raw line: {line}")
            
            # Try to parse as JSON
            try:
                event = json.loads(line)
                raw_events.append(event)
                
                event_type = event.get("type", "unknown")
                print(f"   ✅ Parsed JSON - Type: {event_type}")
                
                # Show key fields for each event type
                if event_type == "tool_use":
                    tool_name = event.get("name", "unknown")
                    tool_id = event.get("id", "unknown")
                    print(f"   🔧 Tool: {tool_name} (ID: {tool_id})")
                    
                elif event_type == "tool_result":
                    tool_use_id = event.get("tool_use_id", "unknown")
                    content = event.get("content", [])
                    print(f"   📤 Tool Result for ID: {tool_use_id}")
                    print(f"   📄 Content: {content}")
                    
                elif event_type == "claude_message":
                    content = event.get("content", "")
                    print(f"   💬 Claude Message: {content[:100]}...")
                    
                elif event_type == "result":
                    result = event.get("result", {})
                    print(f"   🎯 Final Result: {result}")
                    
                else:
                    print(f"   📋 Other fields: {list(event.keys())}")
                    
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON parse error: {e}")
                print(f"   📝 Raw content: {line[:200]}...")
        
        # Wait for process to complete
        process.wait()
        print(f"\n📊 Process completed with return code: {process.returncode}")
        
        # Analyze captured events
        print(f"\n🔍 Event Analysis:")
        print(f"   Total events: {len(raw_events)}")
        
        tool_use_events = [e for e in raw_events if e.get("type") == "tool_use"]
        tool_result_events = [e for e in raw_events if e.get("type") == "tool_result"]
        claude_message_events = [e for e in raw_events if e.get("type") == "claude_message"]
        
        print(f"   Tool use events: {len(tool_use_events)}")
        print(f"   Tool result events: {len(tool_result_events)}")
        print(f"   Claude message events: {len(claude_message_events)}")
        
        # Show tool use/result matching
        if tool_use_events:
            print(f"\n🔧 Tool Use Events:")
            for i, event in enumerate(tool_use_events):
                tool_id = event.get("id", "unknown")
                tool_name = event.get("name", "unknown")
                print(f"   {i+1}. {tool_name} (ID: {tool_id})")
        
        if tool_result_events:
            print(f"\n📤 Tool Result Events:")
            for i, event in enumerate(tool_result_events):
                tool_use_id = event.get("tool_use_id", "unknown")
                content = event.get("content", [])
                print(f"   {i+1}. For tool ID: {tool_use_id}")
                print(f"      Content: {content}")
        else:
            print(f"\n❌ NO TOOL RESULT EVENTS FOUND!")
            print(f"   This explains why tool outputs show 'No data' in LangSmith")
        
        # Check if tool results are embedded in Claude messages
        if claude_message_events and not tool_result_events:
            print(f"\n🔍 Checking if tool results are embedded in Claude messages...")
            for i, event in enumerate(claude_message_events):
                content = str(event.get("content", ""))
                if "apps/" in content or "README" in content or "directory" in content:
                    print(f"   Message {i+1} contains tool-like output:")
                    print(f"   {content[:300]}...")
        
        return len(tool_result_events) > 0
        
    except Exception as e:
        print(f"❌ Raw streaming debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(debug_raw_streaming())
    
    print(f"\n🎯 Conclusion:")
    if success:
        print("✅ Tool result events are being generated correctly")
        print("   The issue might be in the parsing logic")
    else:
        print("❌ Tool result events are NOT being generated by Claude Code")
        print("   This explains the 'No data' issue in LangSmith")
        print("   Tool outputs might be embedded in Claude messages instead")
