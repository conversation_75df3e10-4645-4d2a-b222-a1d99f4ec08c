#!/usr/bin/env python3
"""
Test script to verify Lang<PERSON>mith tracing is working properly.
"""

import logging
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_langsmith_config():
    """Check if LangSmith environment variables are properly configured."""
    print("🔍 LangSmith Configuration Check:")
    print("=" * 50)
    
    # Check required environment variables
    required_vars = [
        'LANGSMITH_TRACING',
        'LANGSMITH_ENDPOINT', 
        'LANGSMITH_API_KEY',
        'LANGSMITH_PROJECT'
    ]
    
    all_set = True
    for var in required_vars:
        value = os.getenv(var)
        if var == 'LANGSMITH_API_KEY' and value:
            # Mask API key for security
            display_value = f"***{value[-4:]}"
        else:
            display_value = value
            
        status = "✅" if value else "❌"
        print(f"  {status} {var}: {display_value}")
        
        if not value:
            all_set = False
    
    print("=" * 50)
    
    if all_set:
        print("✅ All LangSmith environment variables are set!")
        
        # Check if tracing is enabled
        if os.getenv('LANGSMITH_TRACING', '').lower() == 'true':
            print("🎯 LangSmith tracing is ENABLED")
        else:
            print("⚠️  LangSmith tracing is DISABLED")
            
        return True
    else:
        print("❌ Some LangSmith environment variables are missing!")
        return False

async def test_modal_tracing():
    """Test Modal sandbox tracing specifically."""
    print("\n🧪 Testing Modal Sandbox Tracing:")
    print("=" * 50)

    try:
        # Import Modal sandbox functions
        from sandbox.modal_sandbox import check_tracing_config, run_claude_with_tracing, create_sandbox

        # Check tracing configuration
        config = check_tracing_config()
        print("📊 Tracing Configuration:")
        for key, value in config.items():
            status = "✅" if value else "❌"
            print(f"  {status} {key}: {value}")

        if not (config["langsmith_available"] and config["tracing_enabled"] and config["api_key_set"]):
            print("⚠️  Tracing not fully configured, but continuing with test...")

        # Create a sandbox for testing
        print("\n🚀 Creating sandbox for tracing test...")
        sandbox = await create_sandbox(
            repo_id="1",
            timeout=300,
            use_snapshot=True,
        )

        try:
            # Test a simple Claude command with tracing
            print("🤖 Running Claude with tracing enabled...")
            session = await run_claude_with_tracing(
                sandbox=sandbox,
                prompt="List the files in the current directory and tell me what type of project this is.",
                timeout=60,
                enable_tracing=True,
            )

            print(f"✅ Claude session completed!")
            print(f"📊 Session ID: {session.session_id}")
            print(f"⏱️  Duration: {session.elapsed_time:.2f}s")
            print(f"📝 Outputs: {len(session.outputs)}")
            print(f"🎯 Tracing enabled: {session.trace_enabled}")
            print(f"🔗 Run tree: {'Yes' if session.run_tree else 'No'}")

            if session.trace_enabled and session.run_tree:
                print(f"🌳 Child runs created: {len(session.child_runs)}")
                print("🎉 LangSmith tracing is working!")
            else:
                print("⚠️  Tracing was not enabled for this session")

            return True

        finally:
            # Clean up sandbox
            from sandbox.modal_sandbox import cleanup_sandbox
            await cleanup_sandbox(sandbox)
            print("🧹 Sandbox cleaned up")

    except Exception as e:
        print(f"❌ Modal tracing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_graph_with_tracing():
    """Test a simple graph execution to verify tracing works."""
    print("\n🧪 Testing Graph Execution with Tracing:")
    print("=" * 50)

    try:
        # Import after environment variables are loaded
        from workflows.code.graph import graph
        from workflows.code.states import AgentState

        # Create a simple test state
        initial_state: AgentState = {
            "sandbox": None,
            "repository_id": "1",
            "issue_id": "2",
            "pr_url": None,
            "error": None,
        }

        print("🚀 Starting graph execution...")
        print(f"📊 Project: {os.getenv('LANGSMITH_PROJECT')}")
        print(f"🔗 Endpoint: {os.getenv('LANGSMITH_ENDPOINT')}")

        # Execute the graph
        result = await graph.ainvoke(initial_state)

        print("✅ Graph execution completed successfully!")
        print(f"📋 Result: {result}")

        # Check if we can access LangSmith
        try:
            import langsmith
            client = langsmith.Client()
            print(f"🔗 LangSmith client initialized successfully")
            print(f"📊 Connected to project: {client.api_url}")
        except Exception as e:
            print(f"⚠️  LangSmith client error: {e}")

        return True

    except Exception as e:
        print(f"❌ Graph execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔬 LangSmith Tracing Test")
    print("=" * 60)

    # Step 1: Check configuration
    config_ok = check_langsmith_config()

    if not config_ok:
        print("\n❌ Configuration check failed. Please fix environment variables.")
        return

    # Step 2: Test Modal sandbox tracing
    import asyncio
    print("\n🧪 Running Modal tracing test...")
    modal_success = asyncio.run(test_modal_tracing())

    # Step 3: Test full graph execution (optional, takes longer)
    print("\n🧪 Running full graph test...")
    graph_success = asyncio.run(test_simple_graph_with_tracing())

    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"  Modal Tracing: {'✅ PASS' if modal_success else '❌ FAIL'}")
    print(f"  Graph Execution: {'✅ PASS' if graph_success else '❌ FAIL'}")

    if modal_success and graph_success:
        print("\n🎉 All tests passed! LangSmith tracing should be working.")
        print(f"🌐 Check your traces at: {os.getenv('LANGSMITH_ENDPOINT')}")
        print(f"📊 Project: {os.getenv('LANGSMITH_PROJECT')}")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")
        if modal_success:
            print("✅ Modal tracing is working - the issue might be with graph-level tracing")
        if graph_success:
            print("✅ Graph execution is working - the issue might be with Modal-specific tracing")

if __name__ == "__main__":
    main()
