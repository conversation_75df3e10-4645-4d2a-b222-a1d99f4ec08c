#!/usr/bin/env python3
"""
Test script to verify Lang<PERSON>mith tracing is working properly.
"""

import logging
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_langsmith_config():
    """Check if LangSmith environment variables are properly configured."""
    print("🔍 LangSmith Configuration Check:")
    print("=" * 50)
    
    # Check required environment variables
    required_vars = [
        'LANGSMITH_TRACING',
        'LANGSMITH_ENDPOINT', 
        'LANGSMITH_API_KEY',
        'LANGSMITH_PROJECT'
    ]
    
    all_set = True
    for var in required_vars:
        value = os.getenv(var)
        if var == 'LANGSMITH_API_KEY' and value:
            # Mask API key for security
            display_value = f"***{value[-4:]}"
        else:
            display_value = value
            
        status = "✅" if value else "❌"
        print(f"  {status} {var}: {display_value}")
        
        if not value:
            all_set = False
    
    print("=" * 50)
    
    if all_set:
        print("✅ All LangSmith environment variables are set!")
        
        # Check if tracing is enabled
        if os.getenv('LANGSMITH_TRACING', '').lower() == 'true':
            print("🎯 LangSmith tracing is ENABLED")
        else:
            print("⚠️  LangSmith tracing is DISABLED")
            
        return True
    else:
        print("❌ Some LangSmith environment variables are missing!")
        return False

async def test_simple_graph_with_tracing():
    """Test a simple graph execution to verify tracing works."""
    print("\n🧪 Testing Graph Execution with Tracing:")
    print("=" * 50)
    
    try:
        # Import after environment variables are loaded
        from workflows.code.graph import graph
        from workflows.code.states import AgentState
        
        # Create a simple test state
        initial_state: AgentState = {
            "sandbox": None,
            "repository_id": "1",
            "issue_id": "2", 
            "pr_url": None,
            "error": None,
        }
        
        print("🚀 Starting graph execution...")
        print(f"📊 Project: {os.getenv('LANGSMITH_PROJECT')}")
        print(f"🔗 Endpoint: {os.getenv('LANGSMITH_ENDPOINT')}")
        
        # Execute the graph
        result = await graph.ainvoke(initial_state)
        
        print("✅ Graph execution completed successfully!")
        print(f"📋 Result: {result}")
        
        # Check if we can access LangSmith
        try:
            import langsmith
            client = langsmith.Client()
            print(f"🔗 LangSmith client initialized successfully")
            print(f"📊 Connected to project: {client.api_url}")
        except Exception as e:
            print(f"⚠️  LangSmith client error: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ Graph execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔬 LangSmith Tracing Test")
    print("=" * 60)
    
    # Step 1: Check configuration
    config_ok = check_langsmith_config()
    
    if not config_ok:
        print("\n❌ Configuration check failed. Please fix environment variables.")
        return
    
    # Step 2: Test graph execution
    import asyncio
    success = asyncio.run(test_simple_graph_with_tracing())
    
    if success:
        print("\n🎉 All tests passed! LangSmith tracing should be working.")
        print(f"🌐 Check your traces at: {os.getenv('LANGSMITH_ENDPOINT')}")
        print(f"📊 Project: {os.getenv('LANGSMITH_PROJECT')}")
    else:
        print("\n❌ Tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
