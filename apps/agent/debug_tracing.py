#!/usr/bin/env python3
"""
Debug script to test LangSmith tracing initialization.
"""

import logging
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_langsmith_imports():
    """Test LangSmith imports."""
    print("🧪 Testing LangSmith Imports:")
    print("=" * 40)
    
    try:
        from langsmith import Client
        print("✅ langsmith.Client imported successfully")
        
        from langsmith.run_trees import RunTree
        print("✅ langsmith.run_trees.RunTree imported successfully")
        
        return True, Client, RunTree
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False, None, None

def test_client_creation():
    """Test LangSmith client creation."""
    print("\n🧪 Testing Client Creation:")
    print("=" * 40)
    
    try:
        from langsmith import Client
        client = Client()
        print(f"✅ Client created successfully")
        print(f"🔗 API URL: {client.api_url}")
        return True, client
    except Exception as e:
        print(f"❌ Client creation failed: {e}")
        return False, None

def test_runtree_creation():
    """Test RunTree creation."""
    print("\n🧪 Testing RunTree Creation:")
    print("=" * 40)
    
    try:
        from langsmith.run_trees import RunTree
        
        project_name = os.getenv("LANGSMITH_PROJECT", "test-project")
        
        # Test basic RunTree creation
        run_tree = RunTree(
            name="Test Run",
            run_type="chain",
            inputs={"test": "input"},
            project_name=project_name,
            extra={"description": "Test run tree"}
        )
        
        print(f"✅ RunTree created successfully")
        print(f"📊 Name: {run_tree.name}")
        print(f"🔗 Project: {project_name}")
        
        # Test posting
        try:
            run_tree.post()
            print("✅ RunTree posted successfully")
            
            # Test ending
            run_tree.end(outputs={"test": "output"})
            run_tree.patch()
            print("✅ RunTree ended and patched successfully")
            
            return True, run_tree
        except Exception as e:
            print(f"⚠️  RunTree post/end failed: {e}")
            return False, run_tree
            
    except Exception as e:
        print(f"❌ RunTree creation failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False, None

def test_child_runtree():
    """Test child RunTree creation."""
    print("\n🧪 Testing Child RunTree Creation:")
    print("=" * 40)
    
    try:
        from langsmith.run_trees import RunTree
        
        project_name = os.getenv("LANGSMITH_PROJECT", "test-project")
        
        # Create parent
        parent = RunTree(
            name="Parent Run",
            run_type="chain",
            inputs={"parent": "input"},
            project_name=project_name
        )
        parent.post()
        print("✅ Parent RunTree created and posted")
        
        # Create child
        child = parent.create_child(
            name="Child Run",
            run_type="tool",
            inputs={"child": "input"}
        )
        child.post()
        print("✅ Child RunTree created and posted")
        
        # End both
        child.end(outputs={"child": "output"})
        child.patch()
        
        parent.end(outputs={"parent": "output"})
        parent.patch()
        
        print("✅ Both RunTrees ended successfully")
        return True
        
    except Exception as e:
        print(f"❌ Child RunTree test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Main test function."""
    print("🔬 LangSmith Tracing Debug")
    print("=" * 50)
    
    # Check environment
    print("🔍 Environment Check:")
    api_key = os.getenv("LANGSMITH_API_KEY")
    project = os.getenv("LANGSMITH_PROJECT")
    tracing = os.getenv("LANGSMITH_TRACING")
    
    print(f"  API Key: {'✅ Set' if api_key else '❌ Missing'}")
    print(f"  Project: {project or '❌ Missing'}")
    print(f"  Tracing: {tracing or '❌ Missing'}")
    
    if not all([api_key, project, tracing]):
        print("❌ Missing required environment variables")
        return
    
    # Test imports
    imports_ok, Client, RunTree = test_langsmith_imports()
    if not imports_ok:
        return
    
    # Test client
    client_ok, client = test_client_creation()
    if not client_ok:
        return
    
    # Test RunTree
    runtree_ok, run_tree = test_runtree_creation()
    if not runtree_ok:
        print("❌ Basic RunTree test failed")
        return
    
    # Test child RunTree
    child_ok = test_child_runtree()
    if not child_ok:
        print("❌ Child RunTree test failed")
        return
    
    print("\n🎉 All tests passed! LangSmith tracing should work.")

if __name__ == "__main__":
    main()
