#!/usr/bin/env python3
"""
Debug notebook tracing to check Modal streaming and tool outputs.
"""

import asyncio
import logging
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def debug_notebook_workflow():
    """Debug the notebook workflow with detailed tracing."""
    print("🔍 Debugging notebook workflow with tracing...")
    
    try:
        # Import the workflow components
        from workflows.code.graph import graph
        from workflows.code.states import AgentState
        
        # Check LangSmith configuration
        print("\n📊 LangSmith Configuration:")
        print(f"  LANGSMITH_TRACING: {os.getenv('LANGSMITH_TRACING')}")
        print(f"  LANGSMITH_API_KEY: {'✅ Set' if os.getenv('LANGSMITH_API_KEY') else '❌ Not set'}")
        print(f"  LANGSMITH_PROJECT: {os.getenv('LANGSMITH_PROJECT', 'default')}")
        
        # Check Anthropic configuration
        print("\n🤖 Anthropic Configuration:")
        print(f"  ANTHROPIC_API_KEY: {'✅ Set' if os.getenv('ANTHROPIC_API_KEY') else '❌ Not set'}")
        
        # Prepare initial state
        initial_state: AgentState = {
            "sandbox": None,
            "repository_id": "1",
            "issue_id": "2",
            "pr_url": None,
            "error": None,
            "claude_options": {},
            "max_retries": 1,  # Reduce retries for faster debugging
        }
        
        print(f"\n🚀 Starting workflow with state: {initial_state}")
        
        # Execute the workflow
        print("\n⚡ Executing workflow...")
        result = await graph.ainvoke(initial_state)
        
        print(f"\n✅ Workflow completed!")
        print(f"📊 Final result: {result}")
        
        # Check if we got a sandbox
        if result.get("sandbox"):
            print("✅ Sandbox was created successfully")
        else:
            print("❌ No sandbox in result")
            
        # Check for errors
        if result.get("error"):
            print(f"❌ Error in result: {result['error']}")
        else:
            print("✅ No errors in result")
            
        # Check for PR URL
        if result.get("pr_url"):
            print(f"✅ PR URL generated: {result['pr_url']}")
        else:
            print("ℹ️ No PR URL in result")
            
        return True
        
    except Exception as e:
        print(f"❌ Workflow failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def debug_modal_streaming():
    """Test Modal streaming specifically."""
    print("\n🔍 Testing Modal streaming directly...")
    
    try:
        from sandbox.modal_sandbox import create_sandbox, run_claude
        
        # Create sandbox
        print("🚀 Creating sandbox...")
        sandbox = await create_sandbox(repo_id="1", timeout=300, use_snapshot=True)
        print("✅ Sandbox created")
        
        # Test simple Claude command with streaming
        print("\n⚡ Testing Claude Code streaming...")
        simple_prompt = "List the files in the current directory and tell me what you see."
        
        session = await run_claude(
            sandbox=sandbox,
            prompt=simple_prompt,
            claude_options={},
            timeout=60,
            max_retries=1,
            enable_tracing=True,
            stream=True,  # Ensure streaming is enabled
        )
        
        print(f"\n📊 Session Results:")
        print(f"  Success: {session.success}")
        print(f"  Error: {session.error}")
        print(f"  Outputs count: {len(session.outputs) if session.outputs else 0}")
        
        if session.outputs:
            print(f"\n📝 Session Outputs (first 3):")
            for i, output in enumerate(session.outputs[:3]):
                print(f"  {i+1}. Type: {output.type}")
                print(f"     Content: {str(output.content)[:200]}...")
                print(f"     Timestamp: {output.timestamp}")
                
        # Check if we got tool calls
        tool_calls = [o for o in session.outputs if o.type == "tool_call"]
        tool_results = [o for o in session.outputs if o.type == "tool_result"]
        
        print(f"\n🔧 Tool Analysis:")
        print(f"  Tool calls: {len(tool_calls)}")
        print(f"  Tool results: {len(tool_results)}")
        
        if tool_calls:
            print(f"  First tool call: {tool_calls[0].content}")
        if tool_results:
            print(f"  First tool result: {tool_results[0].content}")
            
        return True
        
    except Exception as e:
        print(f"❌ Modal streaming test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    async def main():
        print("🧪 Starting comprehensive debugging...")
        
        # Test 1: Modal streaming
        streaming_success = await debug_modal_streaming()
        
        # Test 2: Full workflow
        workflow_success = await debug_notebook_workflow()
        
        print(f"\n🎯 Debug Summary:")
        print(f"  Modal streaming: {'✅ Working' if streaming_success else '❌ Failed'}")
        print(f"  Workflow execution: {'✅ Working' if workflow_success else '❌ Failed'}")
        
        if streaming_success and workflow_success:
            print("\n🎉 Everything looks good! Check your LangSmith dashboard for traces.")
        else:
            print("\n⚠️ Issues detected. Check the logs above for details.")
    
    asyncio.run(main())
