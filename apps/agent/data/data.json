{"integrations": [{"id": 1, "created_at": "2025-06-15", "name": "github", "org_id": 1, "data": {"github_org_name": "backspace-org", "github_org_type": "Organization", "installation_id": "73947948"}}], "repositories": [{"id": 1, "created_at": "2025-06-16 08:26:12.532688+00", "url": "https://github.com/backspace-org/backspace-mono.git", "org_id": 1, "name": "backspace-mono", "repo_created_at": "2025-05-25 16:41:07+00", "pushed_at": "2025-06-16 05:18:47+00", "integration_id": 1, "modal_snapshot_id": "im-RviWz88FekA8F9ZW1qZyhg"}], "issues": [{"issue_id": "1", "title": "Create README.md for apps/web only", "description": "Create a README.md file for the apps/web directory only.", "status": "pending", "sub_issues": [{"sub_issue_id": "1", "title": "Analyze project architecture and structure", "description": "Traverse the codebase to understand the overall architecture, identify main modules, components, and their organization patterns."}, {"sub_issue_id": "2", "title": "Document API endpoints and database schema", "description": "Identify and document all API endpoints, database tables, and data models used throughout the application."}, {"sub_issue_id": "3", "title": "Map component relationships and dependencies", "description": "Analyze how different components interact with each other, document data flow, and identify key dependencies between modules."}, {"sub_issue_id": "4", "title": "Document configuration and setup requirements", "description": "Identify configuration files, environment variables, and document the setup process including dependencies and build steps."}, {"sub_issue_id": "5", "title": "Create usage examples and code patterns", "description": "Generate practical code examples showing how to use key features and identify common patterns used throughout the codebase."}]}, {"issue_id": "2", "title": "Delete the README.md file and finish", "description": "Delete the README.md file and finish the issue.", "status": "pending", "sub_issues": []}]}