"""Convex client for accessing data from Convex database."""

import logging
import os
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from convex import ConvexClient
from dotenv import load_dotenv

logger = logging.getLogger(__name__)
load_dotenv()


class ConvexDataClient:
    """Client for accessing data from Convex database."""

    def __init__(self, deployment_url: Optional[str] = None):
        """Initialize the Convex client.

        Args:
            deployment_url: Convex deployment URL. If None, uses CONVEX_URL from env.
        """
        self.deployment_url = deployment_url or os.getenv("CONVEX_URL")
        if not self.deployment_url:
            raise ValueError("CONVEX_URL environment variable is required")
        
        self.client = ConvexClient(self.deployment_url)
        logger.info(f"✅ Initialized Convex client for {self.deployment_url}")

    async def get_installation_id_and_repo_url(self, repo_id: str) -> <PERSON>ple[int, str]:
        """Get GitHub installation ID and repository URL from repository ID via Convex.

        Args:
            repo_id: Repository ID (Convex ID) to lookup

        Returns:
            Tuple of (GitHub installation ID, repository URL)

        Raises:
            ValueError: If repo_id not found or missing installation_id
        """
        try:
            # Call the Convex query that joins repository with integration
            # Note: Convex Python client's query method is synchronous
            repo_data = self.client.query(
                "repositories/queries:getRepositoryAndInstallationId", 
                {"id": repo_id}
            )
            
            if not repo_data:
                raise ValueError(f"Repository with id {repo_id} not found")
            
            installation_id = repo_data.get("installation_id")
            repo_url = repo_data.get("url")
            
            if not installation_id:
                raise ValueError(f"No installation_id found for repository {repo_id}")
            
            if not repo_url:
                raise ValueError(f"No URL found for repository {repo_id}")
            
            try:
                installation_id = int(installation_id)
            except ValueError:
                raise ValueError(f"Invalid installation_id format: {installation_id}")
            
            logger.debug(
                f"🔑 Found installation_id {installation_id} and repo_url {repo_url} for repo {repo_id}"
            )
            
            return installation_id, repo_url
            
        except Exception as e:
            logger.error(f"Failed to get installation_id and repo_url for repo {repo_id}: {e}")
            raise

    async def update_repository_snapshot_id(self, repo_id: str, snapshot_id: str) -> bool:
        """Update the modal_snapshot_id for a repository.

        Args:
            repo_id: Repository ID (Convex ID) to update
            snapshot_id: New Modal snapshot ID to set

        Returns:
            True if update was successful, False otherwise

        Raises:
            Exception: If update fails
        """
        try:
            # Note: Convex Python client's mutation method is synchronous
            result = self.client.mutation(
                "repositories/mutations:updateRepositoryById",
                {
                    "id": repo_id,
                    "data": {
                        "modal_snapshot_id": snapshot_id
                    }
                }
            )
            
            if result and result.get("success"):
                logger.info(f"✅ Updated repository {repo_id} with snapshot_id: {snapshot_id}")
                return True
            else:
                logger.error(f"❌ Failed to update repository {repo_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to update repository snapshot ID: {e}")
            raise

    def get_repository_by_id(self, repo_id: str) -> Optional[Dict[str, Any]]:
        """Get repository by ID synchronously.

        Args:
            repo_id: Repository ID to search for

        Returns:
            Repository data if found, None otherwise
        """
        try:
            # Convex Python client supports sync calls too
            return self.client.query("repositories/queries:getRepositoryById", {"id": repo_id})
        except Exception as e:
            logger.warning(f"Repository with id {repo_id} not found: {e}")
            return None


# Create a singleton instance
convex_client = ConvexDataClient()