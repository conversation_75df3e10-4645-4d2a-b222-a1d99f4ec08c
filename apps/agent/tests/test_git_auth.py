#!/usr/bin/env python3
"""Test script to verify GitHub authentication fix in modal_sandbox.py"""

import asyncio
import logging
import os
import sys

sys.path.append("./agent/sandbox")

from agent.sandbox.modal_sandbox import (
    create_installation_token,
    get_installation_id_from_repo,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_github_auth():
    """Test GitHub authentication and token generation."""
    try:
        # Test 1: Get installation ID from repo
        logger.info("🔍 Testing installation ID lookup...")
        repo_id = os.getenv("DEFAULT_REPO_ID", "990171565")
        installation_id, repo_url = await get_installation_id_from_repo(repo_id)
        logger.info(f"✅ Found installation_id: {installation_id}")
        logger.info(f"✅ Found repo_url: {repo_url}")

        # Test 2: Generate GitHub token
        logger.info("🔑 Testing token generation...")
        token = await create_installation_token(installation_id)
        logger.info(
            f"✅ Generated token: {token[:10]}...{token[-10:] if len(token) > 20 else ''}"
        )

        # Test 3: Verify token format (should be a GitHub token)
        if token.startswith(("ghs_", "ghp_", "gho_")):
            logger.info("✅ Token format looks correct")
        else:
            logger.warning(f"⚠️ Unexpected token format: {token[:20]}...")

        logger.info("🎉 All tests passed!")
        return True

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(test_github_auth())
    sys.exit(0 if success else 1)
