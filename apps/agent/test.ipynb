{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO: ✅ Loaded data from /Users/<USER>/backspace/convex-monorepo/apps/agent/data/data.json\n", "INFO: 🚀 Creating sandbox for code agent...\n", "INFO: 📍 Repository ID: 1\n", "INFO: 🚀 Creating Modal sandbox...\n", "INFO: Generating installation token for installation 71548156\n", "INFO: ✅ Generated token for installation 71548156\n", "INFO: 📸 Trying existing snapshot: im-8SZ6xnSPxi7YaqGyI0yMqt\n", "INFO: ✅ Using existing snapshot\n", "INFO: 📦 Initializing Modal sandbox (timeout: 3600s)...\n", "INFO: 🖼️  Using image: Image.from_id('im-8SZ6xnSPxi7YaqGyI0yMqt') (ID: N/A)\n", "INFO: ✅ Successfully created Modal sandbox\n", "INFO: 🤖 Verifying Claude Code installation...\n", "INFO: ✅ Claude Code version: 1.0.39 (<PERSON>)\n", "INFO: 🔧 Running MCP configuration script...\n", "INFO: MCP config output: Configuring MCP servers...\n", "Added stdio MCP server puppeteer with command: npx -y @modelcontextprotocol/server-puppeteer to local config\n", "Added stdio MCP server context7 with command: npx -y @smithery/cli@latest run @upstash/context7-mcp --key $SMITHERLY_KEY to local config\n", "MCP servers configured successfully\n", "\n", "INFO: 🔌 Verifying MCP server configuration...\n", "INFO: 📋 MCP servers configured:\n", "puppeteer: npx -y @modelcontextprotocol/server-puppeteer\n", "context7: npx -y @smithery/cli@latest run @upstash/context7-mcp --key $SMITHERLY_KEY\n", "\n", "INFO: ✅ Found MCP server: puppeteer\n", "INFO: ✅ Found MCP server: context7\n", "INFO: 🎉 MCP integration ready with 2 servers\n", "INFO: 🎉 Modal sandbox setup complete!\n", "INFO: 📊 Starting ISSUE phase...\n", "INFO: 🔄 Configured for max 2 retries\n", "INFO: 🌊 Using Modal execution...\n", "INFO: 🤖 Starting Claude Code session: modal-4422716640-1751412376\n", "INFO: 📝 Prompt: \n", "You are assigned the following issue:\n", "<issue>\n", "{'issue_id': '2', 'title': 'Delete the README.md file...\n", "INFO: ⚡ Executing Claude Code...\n", "INFO: 🌊 Streaming output...\n", "INFO: 🚀 SYSTEM INIT\n", "INFO:    📁 CWD: /workspace\n", "INFO:    🤖 Model: claude-sonnet-4-20250514\n", "INFO:    🛡️ Permission Mode: default\n", "INFO: 🤖 <PERSON>: I'll help you complete this issue by deleting the README.md file and creating a pull request.\n", "INFO: ✅ Tool: TodoWrite\n", "INFO: 🔧 Tool: LS\n", "INFO: ✅ Tool: TodoWrite\n", "INFO: ⚡ Running: git checkout -b delete-readme\n", "INFO: ⚡ Running: rm README.md\n", "INFO: ✅ Tool: TodoWrite\n", "INFO: ⚡ Running: git add README.md\n", "INFO: ⚡ Running: git commit -m \"$(cat <<'EOF'\n", "Delete README.md file\n", "\n", "🤖 Generated with [Claude Code](https://claude.ai/code)\n", "\n", "Co-Authored-By: <PERSON> <<EMAIL>>\n", "EOF\n", ")\"\n", "INFO: ⚡ Running: git push -u origin delete-readme\n", "INFO: ✅ Tool: TodoWrite\n", "INFO: ⚡ Running: gh pr create --title \"Delete README.md file\" --body \"$(cat <<'EOF'\n", "## Summary\n", "- Delete the README.md file as requested in issue #2\n", "\n", "## Test plan\n", "- Verify README.md file has been deleted from the repository\n", "\n", "🤖 Generated with [Claude Code](https://claude.ai/code)\n", "EOF\n", ")\"\n", "INFO: ⚡ Running: gh pr create --head delete-readme --title \"Delete README.md file\" --body \"$(cat <<'EOF'\n", "## Summary\n", "- Delete the README.md file as requested in issue #2\n", "\n", "## Test plan\n", "- Verify README.md file has been deleted from the repository\n", "\n", "🤖 Generated with [Claude Code](https://claude.ai/code)\n", "EOF\n", ")\"\n", "INFO: ⚡ Running: git log --oneline -5\n", "INFO: ⚡ Running: gh auth status\n", "INFO: ⚡ Running: git remote -v\n", "INFO: ⚡ Running: git push origin delete-readme\n", "INFO: ⚡ Running: export GH_TOKEN=$(gh auth token) && git push origin delete-readme\n", "INFO: ⚡ Running: gh pr create --base main --head delete-readme --title \"Delete README.md file\" --body \"$(cat <<'EOF'\n", "## Summary\n", "- Delete the README.md file as requested in issue #2\n", "\n", "## Test plan\n", "- Verify README.md file has been deleted from the repository\n", "\n", "🤖 Generated with [Claude Code](https://claude.ai/code)\n", "EOF\n", ")\" --repo backspace-org/backspace-mono\n", "INFO: ⚡ Running: git status\n", "INFO: ⚡ Running: git stash\n", "INFO: ⚡ Running: gh pr create --title \"Delete README.md file\" --body \"$(cat <<'EOF'\n", "## Summary\n", "- Delete the README.md file as requested in issue #2\n", "\n", "## Test plan\n", "- Verify README.md file has been deleted from the repository\n", "\n", "🤖 Generated with [Claude Code](https://claude.ai/code)\n", "EOF\n", ")\"\n", "INFO: ⚡ Running: git config --get remote.origin.url\n", "INFO: ⚡ Running: git config credential.helper store\n", "INFO: ⚡ Running: echo \"https://x-access-token:$(gh auth token)@github.com\" > ~/.git-credentials\n", "INFO: ⚡ Running: git push origin delete-readme\n", "INFO: ⚡ Running: git remote set-url origin https://github.com/backspace-org/backspace-mono.git\n", "INFO: ⚡ Running: gh auth setup-git\n", "INFO: ⚡ Running: git push origin delete-readme\n", "INFO: ⚡ Running: gh pr create --title \"Delete README.md file\" --body \"$(cat <<'EOF'\n", "## Summary\n", "- Delete the README.md file as requested in issue #2\n", "\n", "## Test plan\n", "- Verify README.md file has been deleted from the repository\n", "\n", "🤖 Generated with [Claude Code](https://claude.ai/code)\n", "EOF\n", ")\"\n", "INFO: ✅ Tool: TodoWrite\n", "INFO: 🤖 Claude: <result>\n", "{\n", "  \"pr_url\": \"https://github.com/backspace-org/backspace-mono/pull/104\"\n", "}\n", "</result>\n", "INFO: 📊 RESULT:\n", "INFO:    💰 Cost: $0.3015\n", "INFO:    ⏱️ Duration: 129624ms\n", "INFO:    ✅ Success\n", "INFO: ✅ Claude Code session completed successfully after 1 attempt(s)\n", "INFO: ✅ Claude execution completed with 34 outputs\n", "INFO: ✅ Issue completed successfully\n", "INFO: 🧹 Cleaning up sandbox...\n", "INFO: 🧹 Cleaning up Modal sandbox...\n", "INFO: ✅ Successfully cleaned up Modal sandbox\n", "INFO: ✅ sandbox cleaned up successfully\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Result: {'sandbox': None, 'repository_id': '1', 'issue_id': '2', 'pr_url': 'https://github.com/backspace-org/backspace-mono/pull/104', 'error': None}\n"]}], "source": ["import logging\n", "logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')\n", "\n", "# Test with data client integration\n", "from workflows.code.graph import graph\n", "from workflows.code.states import AgentState\n", "\n", "initial_state: AgentState = {\n", "    \"sandbox\": None,\n", "    \"repository_id\": \"1\",\n", "    \"issue_id\": \"2\",\n", "    \"pr_url\": None,\n", "    \"error\": None,\n", "}\n", "\n", "try:\n", "    result = await graph.ainvoke(initial_state)\n", "    print(f\"✅ Result: {result}\")\n", "except Exception as e:\n", "    print(f\"❌ Error: {e}\")\n", "    import traceback\n", "    traceback.print_exc()\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO: ✅ Loaded data from /Users/<USER>/Documents/projects/backspace/convex-monorepo/apps/agent/data/data.json\n", "INFO: ✅ Initialized Convex client for https://affable-trout-340.convex.cloud\n", "INFO: 🚀 Creating sandbox for Claude-Scanner session...\n", "INFO: 📍 Repository ID: ********************************\n", "INFO: 🚀 Creating Modal sandbox...\n", "INFO: Generating installation token for installation 73947948\n", "INFO: ✅ Generated token for installation 73947948\n", "INFO: 📸 Trying existing snapshot: im-5M2DA7am6tBtNzuCukmiQz\n", "INFO: ✅ Using existing snapshot\n", "INFO: 📦 Initializing Modal sandbox (timeout: 3600s)...\n", "INFO: 🖼️  Using image: Image.from_id('im-5M2DA7am6tBtNzuCukmiQz') (ID: N/A)\n", "INFO: ✅ Successfully created Modal sandbox\n", "INFO: ✅ Claude Code version: 1.0.39 (<PERSON>)\n", "INFO: ✅ MCP configuration file exists\n", "INFO: 🎉 Claude Code and MCP integration ready!\n", "INFO: 🎉 Modal sandbox setup complete!\n", "INFO: 🔍 Starting CLARITY analysis...\n", "INFO: 🌊 Using Modal execution...\n", "INFO: 🤖 Starting Claude Code session: modal-4905263248-1751495086\n", "INFO: 📝 Prompt: \n", "<base_instructions>\n", "You are a staff software engineer, specialized in Vercel, Next.js and Turborepo...\n", "INFO: ⚡ Executing Claude Code...\n", "INFO: 🌊 Streaming output...\n", "INFO: 🚀 SYSTEM INIT\n", "INFO:    📁 CWD: /workspace\n", "INFO:    🤖 Model: claude-sonnet-4-20250514\n", "INFO:    🛡️ Permission Mode: default\n", "INFO: 🤖 <PERSON>: I'll perform a comprehensive clarity analysis of your Next.js codebase. Let me start by examining the project structure and existing documentation.\n", "INFO: 👀 Reading: /workspace/CLAUDE.md\n", "INFO: 🤖 <PERSON>: Let me first explore the project structure to understand the codebase:\n", "INFO: 🔧 Tool: LS\n", "INFO: 👀 Reading: /workspace/package.json\n", "INFO: 👀 Reading: /workspace/tsconfig.json\n", "INFO: 🤖 <PERSON>: Now let me examine the web app's configuration and structure:\n", "INFO: 👀 Reading: /workspace/apps/web/package.json\n", "INFO: 👀 Reading: /workspace/apps/web/tsconfig.json\n", "INFO: 👀 Reading: /workspace/turbo.json\n", "INFO: 🤖 Claude: Now let me perform a quick assessment of code complexity and then examine key files:\n", "INFO: ⚡ Running: find /workspace/apps/web/src -name \"*.tsx\" -o -name \"*.ts\" | xargs wc -l | sort -rn | head -20\n", "INFO: 🔧 Tool: Grep\n", "INFO: 🤖 <PERSON>: Let me examine the largest and most complex files to identify clarity issues:\n", "INFO: 👀 Reading: /workspace/apps/web/src/components/ui/sidebar.tsx\n", "INFO: 👀 Reading: /workspace/apps/web/src/components/ui/dropdown-menu.tsx\n", "INFO: 👀 Reading: /workspace/apps/web/src/app/dashboard/[slug-org]/repositories/page.tsx\n", "ERROR: 💥 Unicode decode error during command execution: 'utf-8' codec can't decode bytes in position 8190-8191: unexpected end of data\n", "WARNING: ⚠️ Claude execution attempt 1 failed: Unicode decode error: 'utf-8' codec can't decode bytes in position 8190-8191: unexpected end of data\n", "INFO: 🔄 Retrying with continue command (1/2)...\n", "INFO: 🔄 Retry attempt 1/2 - Using continue command\n", "INFO: 🔄 Executing Claude Code --continue...\n", "INFO: 🌊 Streaming output...\n", "INFO: 🚀 SYSTEM INIT\n", "INFO:    📁 CWD: /workspace\n", "INFO:    🤖 Model: claude-sonnet-4-20250514\n", "INFO:    🛡️ Permission Mode: default\n", "INFO: 🤖 <PERSON>: I'll continue the clarity analysis. Let me examine more key files and check for common clarity issues:\n", "INFO: 👀 Reading: /workspace/apps/web/src/components/ui/filter-popopver.tsx\n", "INFO: 👀 Reading: /workspace/apps/web/src/app/layout.tsx\n", "INFO: 👀 Reading: /workspace/apps/web/src/components/Header.tsx\n", "INFO: 🤖 <PERSON>: Let me examine some additional files to complete the analysis:\n", "INFO: 👀 Reading: /workspace/apps/web/src/app/dashboard/[slug-org]/repositories/dummy.ts\n", "INFO: 🔧 Tool: Grep\n", "INFO: 🔧 Tool: Grep\n", "INFO: 🤖 Claude: Now let me check for TypeScript interface usage and missing type definitions:\n", "INFO: 🔧 Tool: Grep\n", "INFO: 🔧 Tool: Grep\n", "INFO: 👀 Reading: /workspace/apps/web/src/lib/utils.ts\n", "INFO: 🤖 Claude: Let me check for magic numbers and inline styles, and examine the component structure:\n", "INFO: ⚡ Running: find /workspace/apps/web/src -name \"*.tsx\" -exec grep -l \"style=\" {} \\;\n", "INFO: 👀 Reading: /workspace/apps/web/src/app/ConvexClientProvider.tsx\n", "INFO: 👀 Reading: /workspace/apps/web/src/app/dashboard/[slug-org]/layout.tsx\n", "INFO: 🤖 <PERSON>: Now let me check for missing error boundaries and examine utility functions:\n", "INFO: 👀 Reading: /workspace/apps/web/src/utils/format-timestamp.ts\n", "INFO: 👀 Reading: /workspace/apps/web/src/utils/format-relative-time.ts\n", "INFO: 👀 Reading: /workspace/apps/web/src/app/ErrorBoundary.tsx\n", "INFO: 🤖 Claude: Based on my comprehensive analysis of the Next.js codebase, I can now provide the clarity analysis results:\n", "\n", "<result>\n", "{\n", "  \"score\": 6,\n", "  \"summary\": \"The codebase shows moderate clarity with well-structured UI components using shadcn/ui patterns and proper TypeScript usage. However, clarity is impacted by an extremely large sidebar component (725 lines), typos in filenames, inconsistent formatting, commented-out functionality, debug code, and lack of proper TypeScript interfaces for data structures.\",\n", "  \"issues\": [\n", "    {\n", "      \"locations\": [\"apps/web/src/components/ui/sidebar.tsx:1-725\"],\n", "      \"description\": \"Massive 725-line sidebar component violates single responsibility principle and is difficult to understand and maintain. Contains 25+ sub-components in a single file with complex CSS-in-JS logic and multiple variants.\",\n", "      \"severity\": \"high\",\n", "      \"suggested_fix\": \"Split the sidebar.tsx into multiple focused files: SidebarProvider.tsx, SidebarContent.tsx, SidebarMenu.tsx, SidebarButton.tsx, etc. Extract the CVA variants and constants into separate files. Create a barrel export index.tsx for clean imports.\"\n", "    },\n", "    {\n", "      \"locations\": [\"apps/web/src/components/ui/filter-popopver.tsx:1\", \"apps/web/src/components/Header.tsx:2\"],\n", "      \"description\": \"Filename contains typo 'popopver' instead of 'popover', making it confusing and unprofessional. The typo is propagated in import statements across multiple files.\",\n", "      \"severity\": \"medium\",\n", "      \"suggested_fix\": \"Rename 'filter-popopver.tsx' to 'filter-popover.tsx' and update all import statements. Use consistent kebab-case naming throughout the project.\"\n", "    },\n", "    {\n", "      \"locations\": [\"apps/web/src/app/dashboard/[slug-org]/repositories/page.tsx:17-19\", \"apps/web/src/app/dashboard/[slug-org]/repositories/dummy.ts:1-56\"],\n", "      \"description\": \"Repository data uses dummy/mock data without proper TypeScript interfaces. Data structure is not defined as types, making it unclear what properties are expected and their types.\",\n", "      \"severity\": \"medium\",\n", "      \"suggested_fix\": \"Create proper TypeScript interfaces: `interface Repository { id: number; name: string; type: string; status: 'Pending' | 'Running' | 'Success'; issues: number; branchSource: string; timeStamp: string; }` and apply to dummy data and component props.\"\n", "    },\n", "    {\n", "      \"locations\": [\"apps/web/src/components/ui/filter-popopver.tsx:85\", \"apps/web/src/components/Header.tsx:56\"],\n", "      \"description\": \"Commented-out functional code and debug console.log statements indicate incomplete implementation and debugging artifacts left in production code.\",\n", "      \"severity\": \"medium\",\n", "      \"suggested_fix\": \"<PERSON><PERSON><PERSON> commented `onSelect={onToggle}` code and implement proper functionality. Replace `console.log('test')` with actual organization switching logic or remove if not needed.\"\n", "    },\n", "    {\n", "      \"locations\": [\"apps/web/src/components/Header.tsx:23-38\"],\n", "      \"description\": \"Complex inline JSX component definition with extensive className strings makes the Header component difficult to read. The orgTrigger JSX spans 16 lines with complex Tailwind classes including unusual syntax like '!bg-transparent'.\",\n", "      \"severity\": \"low\",\n", "      \"suggested_fix\": \"Extract the orgTrigger into a separate component like `<OrgSwitcher />` and move complex className logic to CSS modules or a styled component. Use `clsx()` or `cn()` utility for conditional classes instead of inline strings.\"\n", "    }\n", "  ]\n", "}\n", "</result>\n", "INFO: 📊 RESULT:\n", "INFO:    💰 Cost: $0.2134\n", "INFO:    ⏱️ Duration: 54091ms\n", "INFO:    ✅ Success\n", "INFO: ✅ Claude Code session completed successfully after 2 attempt(s)\n", "INFO: ✅ Claude execution completed with 42 outputs\n", "INFO: ✅ CLARITY analysis completed successfully\n", "INFO: 📊 Starting AGGREGATE phase...\n", "INFO: 🌊 Using Modal execution...\n", "INFO: 🤖 Starting Claude Code session: modal-4905263248-1751495172\n", "INFO: 📝 Prompt: You are a staff software engineer specialized in Vercel, Next.js and Turborepo. \n", "    \n", "<analysis_resu...\n", "INFO: ⚡ Executing Claude Code...\n", "INFO: 🌊 Streaming output...\n", "INFO: 🚀 SYSTEM INIT\n", "INFO:    📁 CWD: /workspace\n", "INFO:    🤖 Model: claude-sonnet-4-20250514\n", "INFO:    🛡️ Permission Mode: default\n", "INFO: 🤖 Claude: I'll analyze the codebase issues and create a prioritized, actionable task list. Let me first examine the key files mentioned in the analysis to better understand the current state.\n", "INFO: 👀 Reading: /workspace/apps/web/src/components/ui/sidebar.tsx\n", "INFO: 👀 Reading: /workspace/apps/web/src/components/ui/filter-popopver.tsx\n", "INFO: 👀 Reading: /workspace/apps/web/src/components/Header.tsx\n", "INFO: 👀 Reading: /workspace/apps/web/src/app/dashboard/[slug-org]/repositories/dummy.ts\n", "INFO: 🤖 <PERSON>: Let me check the actual size of the sidebar file to confirm the 725 lines mentioned in the analysis:\n", "INFO: ⚡ Running: wc -l /workspace/apps/web/src/components/ui/sidebar.tsx\n", "INFO: 🤖 <PERSON>: Now let me also check the repository page that uses the dummy data:\n", "INFO: 👀 Reading: /workspace/apps/web/src/app/dashboard/[slug-org]/repositories/page.tsx\n", "INFO: ✅ Claude Code session completed successfully after 1 attempt(s)\n", "INFO: ✅ Claude execution completed with 10 outputs\n", "INFO: ✅ AGGREGATE phase completed successfully\n", "INFO: 🧹 Cleaning up sandbox...\n", "INFO: 🧹 Cleaning up Modal sandbox...\n", "INFO: ✅ Successfully cleaned up Modal sandbox\n", "INFO: ✅ sandbox cleaned up successfully\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Result: {'sandbox': None, 'repository_id': '********************************', 'analysis_results': [{'metric': <MetricType.CLARITY: 'clarity'>, 'score': 6, 'summary': 'The codebase shows moderate clarity with well-structured UI components using shadcn/ui patterns and proper TypeScript usage. However, clarity is impacted by an extremely large sidebar component (725 lines), typos in filenames, inconsistent formatting, commented-out functionality, debug code, and lack of proper TypeScript interfaces for data structures.', 'issues': [{'locations': ['apps/web/src/components/ui/sidebar.tsx:1-725'], 'description': 'Massive 725-line sidebar component violates single responsibility principle and is difficult to understand and maintain. Contains 25+ sub-components in a single file with complex CSS-in-JS logic and multiple variants.', 'severity': 'high', 'suggested_fix': 'Split the sidebar.tsx into multiple focused files: SidebarProvider.tsx, SidebarContent.tsx, SidebarMenu.tsx, SidebarButton.tsx, etc. Extract the CVA variants and constants into separate files. Create a barrel export index.tsx for clean imports.'}, {'locations': ['apps/web/src/components/ui/filter-popopver.tsx:1', 'apps/web/src/components/Header.tsx:2'], 'description': \"Filename contains typo 'popopver' instead of 'popover', making it confusing and unprofessional. The typo is propagated in import statements across multiple files.\", 'severity': 'medium', 'suggested_fix': \"Rename 'filter-popopver.tsx' to 'filter-popover.tsx' and update all import statements. Use consistent kebab-case naming throughout the project.\"}, {'locations': ['apps/web/src/app/dashboard/[slug-org]/repositories/page.tsx:17-19', 'apps/web/src/app/dashboard/[slug-org]/repositories/dummy.ts:1-56'], 'description': 'Repository data uses dummy/mock data without proper TypeScript interfaces. Data structure is not defined as types, making it unclear what properties are expected and their types.', 'severity': 'medium', 'suggested_fix': \"Create proper TypeScript interfaces: `interface Repository { id: number; name: string; type: string; status: 'Pending' | 'Running' | 'Success'; issues: number; branchSource: string; timeStamp: string; }` and apply to dummy data and component props.\"}, {'locations': ['apps/web/src/components/ui/filter-popopver.tsx:85', 'apps/web/src/components/Header.tsx:56'], 'description': 'Commented-out functional code and debug console.log statements indicate incomplete implementation and debugging artifacts left in production code.', 'severity': 'medium', 'suggested_fix': \"Remove commented `onSelect={onToggle}` code and implement proper functionality. Replace `console.log('test')` with actual organization switching logic or remove if not needed.\"}, {'locations': ['apps/web/src/components/Header.tsx:23-38'], 'description': \"Complex inline JSX component definition with extensive className strings makes the Header component difficult to read. The orgTrigger JSX spans 16 lines with complex Tailwind classes including unusual syntax like '!bg-transparent'.\", 'severity': 'low', 'suggested_fix': 'Extract the orgTrigger into a separate component like `<OrgSwitcher />` and move complex className logic to CSS modules or a styled component. Use `clsx()` or `cn()` utility for conditional classes instead of inline strings.'}]}], 'aggregate_result': {'summary': '', 'issues': []}, 'error': None}\n"]}], "source": ["import logging\n", "logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')\n", "\n", "# Test with data client integration\n", "from workflows.scan.graph import graph\n", "from workflows.scan.states import AgentState\n", "\n", "initial_state: AgentState = {\n", "    \"sandbox\": None,\n", "    \"repository_id\": \"********************************\",\n", "    \"analysis_results\": [],\n", "    \"aggregate_result\": None,\n", "    \"error\": None,\n", "}\n", "\n", "try:\n", "    result = await graph.ainvoke(initial_state)\n", "    print(f\"✅ Result: {result}\")\n", "except Exception as e:\n", "    print(f\"❌ Error: {e}\")\n", "    import traceback\n", "    traceback.print_exc()\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}