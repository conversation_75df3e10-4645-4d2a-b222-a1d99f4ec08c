import logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Test with data client integration
from workflows.code.graph import graph
from workflows.code.states import AgentState

initial_state: AgentState = {
    "sandbox": None,
    "repository_id": "1",
    "issue_id": "2",
    "pr_url": None,
    "error": None,
}

try:
    result = await graph.ainvoke(initial_state)
    print(f"✅ Result: {result}")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()


import logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Test with data client integration
from workflows.scan.graph import graph
from workflows.scan.states import AgentState

initial_state: AgentState = {
    "sandbox": None,
    "repository_id": "1",
    "analysis_results": [],
    "aggregate_result": None,
    "error": None,
}

try:
    result = await graph.ainvoke(initial_state)
    print(f"✅ Result: {result}")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
