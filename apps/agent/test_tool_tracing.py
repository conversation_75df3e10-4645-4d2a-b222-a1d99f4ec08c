#!/usr/bin/env python3
"""
Test tool tracing logic to debug the "No data" issue.
"""

import json
from datetime import datetime
from sandbox.modal_sandbox import <PERSON><PERSON><PERSON><PERSON>, ClaudeSession

def test_tool_matching():
    """Test the tool call/result matching logic."""
    print("🧪 Testing tool call/result matching logic...")
    
    # Create a mock session
    session = ClaudeSession("test-session", "test prompt")
    session.trace_enabled = True
    
    # Mock the run_tree and pending_tools
    class MockRunTree:
        def __init__(self):
            self.children = []
            
        def create_child(self, name, run_type, inputs, extra=None):
            child = MockChild(name, run_type, inputs, extra)
            self.children.append(child)
            return child
    
    class MockChild:
        def __init__(self, name, run_type, inputs, extra=None):
            self.name = name
            self.run_type = run_type
            self.inputs = inputs
            self.extra = extra
            self.outputs = None
            self.ended = False
            
        def post(self):
            print(f"  📤 Posted: {self.name}")
            
        def end(self):
            self.ended = True
            print(f"  🏁 Ended: {self.name}")
            
        def patch(self):
            print(f"  🔧 Patched: {self.name} with outputs: {self.outputs}")
    
    session.run_tree = MockRunTree()
    session.pending_tools = {}
    session.child_runs = []
    
    # Simulate a tool_use event
    tool_use_event = {
        "type": "assistant",
        "message": {
            "content": [
                {
                    "type": "tool_use",
                    "id": "toolu_123456789",
                    "name": "TodoWrite",
                    "input": {"task": "Test task", "priority": "high"}
                }
            ]
        }
    }
    
    # Simulate a tool_result event
    tool_result_event = {
        "type": "tool_result",
        "tool_use_id": "toolu_123456789",
        "content": [
            {
                "type": "text",
                "text": "Task created successfully with ID: task_001"
            }
        ]
    }
    
    print("\n1️⃣ Processing tool_use event...")
    
    # Process tool_use (simulate _log_stream logic)
    item = tool_use_event["message"]["content"][0]
    tool = item.get("name")
    tool_id = item.get("id")
    inp = item.get("input", {})
    
    print(f"  🔧 Tool: {tool}")
    print(f"  🆔 Tool ID: {tool_id}")
    print(f"  📥 Input: {inp}")
    
    # Create ClaudeOutput for tool_call
    tool_call_output = ClaudeOutput(
        timestamp=datetime.now().timestamp(),
        type="tool_call",
        content={"name": tool, "input": inp, "id": tool_id},
        raw_event=tool_use_event,
    )
    
    # Process the tool_call output (simulate session.process_output logic)
    if tool_call_output.type == "tool_call" and isinstance(tool_call_output.content, dict):
        tool_run = session.run_tree.create_child(
            name=f"🔧 Tool: {tool_call_output.content.get('name', 'Unknown')}",
            run_type="tool",
            inputs={**tool_call_output.content, "timestamp": tool_call_output.timestamp},
            extra={"raw_event": tool_call_output.raw_event}
        )
        tool_run.post()
        session.pending_tools[tool_call_output.content.get("id")] = tool_run
        session.child_runs.append(tool_run)
        print(f"  ✅ Stored tool with ID: {tool_call_output.content.get('id')}")
        print(f"  📊 Pending tools: {list(session.pending_tools.keys())}")
    
    print("\n2️⃣ Processing tool_result event...")
    
    # Process tool_result (simulate _log_stream logic)
    tool_use_id = tool_result_event.get("tool_use_id")
    content = tool_result_event.get("content", [])
    
    print(f"  🆔 Tool use ID: {tool_use_id}")
    print(f"  📤 Content: {content}")
    
    # Create ClaudeOutput for tool_result
    for item in content:
        if isinstance(item, dict) and item.get("type") == "text":
            text = item.get("text", "")
            print(f"  📝 Result text: {text}")
            
            tool_result_output = ClaudeOutput(
                timestamp=datetime.now().timestamp(),
                type="tool_result",
                content={"tool_use_id": tool_use_id, "result": text},
                raw_event=tool_result_event,
            )
            
            # Process the tool_result output (simulate session.process_output logic)
            if tool_result_output.type == "tool_result" and isinstance(tool_result_output.content, dict):
                print(f"  🔍 Looking for tool with ID: {tool_result_output.content.get('tool_use_id')}")
                print(f"  📊 Available tools: {list(session.pending_tools.keys())}")
                
                parent_tool = session.pending_tools.pop(tool_result_output.content.get("tool_use_id"), None)
                if parent_tool:
                    print(f"  ✅ Found matching tool: {parent_tool.name}")
                    parent_tool.outputs = {
                        **tool_result_output.content,
                        "status": "❌" if tool_result_output.content.get("is_error") else "✅"
                    }
                    parent_tool.name += f" {'❌' if tool_result_output.content.get('is_error') else '✅'}"
                    parent_tool.end()
                    parent_tool.patch()
                    print(f"  🎉 Tool result processed successfully!")
                else:
                    print(f"  ❌ No matching tool found!")
                    print(f"  🔍 Searched for: {tool_result_output.content.get('tool_use_id')}")
                    print(f"  📊 Available: {list(session.pending_tools.keys())}")
    
    print("\n📊 Final Results:")
    print(f"  🏃 Child runs created: {len(session.child_runs)}")
    print(f"  📋 Pending tools remaining: {len(session.pending_tools)}")
    
    for i, child in enumerate(session.child_runs):
        print(f"  {i+1}. {child.name} - Ended: {child.ended} - Outputs: {child.outputs is not None}")
    
    return len(session.child_runs) > 0 and all(child.ended for child in session.child_runs)

if __name__ == "__main__":
    success = test_tool_matching()
    print(f"\n🎯 Test {'PASSED' if success else 'FAILED'}")
