#!/usr/bin/env python3
"""
Test UTF-8 error handling in the scan workflow.
"""

import asyncio
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

async def test_scan_with_utf8_handling():
    """Test the scan workflow with UTF-8 error handling."""
    print("🔍 Testing scan workflow with UTF-8 error handling...")
    
    try:
        from workflows.scan.graph import graph
        from workflows.scan.states import AgentState
        
        # Use a smaller, more controlled test
        initial_state: AgentState = {
            "sandbox": None,
            "repository_id": "1",
            "analysis_results": [],
            "aggregate_result": None,
            "error": None,
            "claude_options": {},  # Remove unsupported options
        }
        
        print(f"🚀 Starting scan workflow with UTF-8 handling...")
        print(f"📊 Initial state: {initial_state}")
        
        # Execute the workflow
        result = await graph.ainvoke(initial_state)
        
        print(f"\n✅ Scan workflow completed!")
        print(f"📊 Final result keys: {list(result.keys())}")
        
        # Check results
        if result.get("error"):
            print(f"❌ Error in result: {result['error']}")
            return False
        else:
            print("✅ No errors in result")
            
        if result.get("analysis_results"):
            print(f"✅ Analysis results: {len(result['analysis_results'])} items")
        else:
            print("ℹ️ No analysis results")
            
        if result.get("aggregate_result"):
            print("✅ Aggregate result generated")
        else:
            print("ℹ️ No aggregate result")
            
        return True
        
    except UnicodeDecodeError as e:
        print(f"❌ Unicode decode error (should be handled now): {e}")
        return False
    except Exception as e:
        print(f"❌ Workflow failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_scan_with_utf8_handling())
    
    print(f"\n🎯 Test Result:")
    if success:
        print("✅ UTF-8 handling is working!")
        print("   The scan workflow completed without Unicode decode errors")
    else:
        print("❌ UTF-8 handling test failed")
        print("   Check the logs above for details")
