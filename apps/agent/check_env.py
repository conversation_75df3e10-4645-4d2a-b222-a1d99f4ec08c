#!/usr/bin/env python3
"""
Simple script to check if environment variables are loaded correctly.
"""

import os
from dotenv import load_dotenv

def main():
    print("🔍 Environment Variable Check")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    print("✅ Loaded .env file")
    
    # Check LangSmith variables
    langsmith_vars = {
        'LANGSMITH_TRACING': os.getenv('LANGSMITH_TRACING'),
        'LANGSMITH_ENDPOINT': os.getenv('LANGSMITH_ENDPOINT'),
        'LANGSMITH_API_KEY': os.getenv('LANGSMITH_API_KEY'),
        'LANGSMITH_PROJECT': os.getenv('LANGSMITH_PROJECT'),
    }
    
    print("\n📊 LangSmith Configuration:")
    for key, value in langsmith_vars.items():
        if key == 'LANGSMITH_API_KEY' and value:
            display_value = f"***{value[-4:]}"
        else:
            display_value = value
        
        status = "✅" if value else "❌"
        print(f"  {status} {key}: {display_value}")
    
    # Check other important variables
    print("\n🔧 Other Configuration:")
    other_vars = {
        'ANTHROPIC_API_KEY': os.getenv('ANTHROPIC_API_KEY'),
        'DEFAULT_REPO_ID': os.getenv('DEFAULT_REPO_ID'),
        'GITHUB_APP_ID': os.getenv('GITHUB_APP_ID'),
    }
    
    for key, value in other_vars.items():
        if 'API_KEY' in key and value:
            display_value = f"***{value[-4:]}"
        else:
            display_value = value
        
        status = "✅" if value else "❌"
        print(f"  {status} {key}: {display_value}")
    
    # Test LangSmith import
    print("\n🧪 Testing LangSmith Import:")
    try:
        import langsmith
        print("✅ langsmith imported successfully")
        
        # Try to create client
        try:
            client = langsmith.Client()
            print(f"✅ LangSmith client created successfully")
            print(f"🔗 API URL: {client.api_url}")
        except Exception as e:
            print(f"⚠️  LangSmith client creation failed: {e}")
            
    except ImportError as e:
        print(f"❌ Failed to import langsmith: {e}")
    
    print("\n" + "=" * 50)
    
    # Summary
    all_langsmith_set = all(langsmith_vars.values())
    if all_langsmith_set:
        print("🎉 All LangSmith variables are set!")
        if os.getenv('LANGSMITH_TRACING', '').lower() == 'true':
            print("🎯 Tracing is ENABLED - you should see traces in LangSmith!")
        else:
            print("⚠️  Tracing is DISABLED - set LANGSMITH_TRACING=true to enable")
    else:
        print("❌ Some LangSmith variables are missing - tracing won't work")

if __name__ == "__main__":
    main()
