#!/usr/bin/env python3
"""
Simple script to check if environment variables are loaded correctly.
"""

import os
from dotenv import load_dotenv

def main():
    print("🔍 Environment Variable Check")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    print("✅ Loaded .env file")
    
    # Check LangSmith variables (both LANGSMITH_* and LANGCHAIN_* variants)
    langsmith_vars = {
        'LANGSMITH_TRACING': os.getenv('LANGSMITH_TRACING'),
        'LANGSMITH_ENDPOINT': os.getenv('LANGSMITH_ENDPOINT'),
        'LANGSMITH_API_KEY': os.getenv('LANGSMITH_API_KEY'),
        'LANGSMITH_PROJECT': os.getenv('LANGSMITH_PROJECT'),
        'LANGCHAIN_TRACING_V2': os.getenv('LANGCHAIN_TRACING_V2'),
        'LANGCHAIN_ENDPOINT': os.getenv('LANGCHAIN_ENDPOINT'),
        'LANGCHAIN_API_KEY': os.getenv('LANGCHAIN_API_KEY'),
        'LANGCHAIN_PROJECT': os.getenv('LANGCHAIN_PROJECT'),
    }
    
    print("\n📊 LangSmith Configuration:")
    for key, value in langsmith_vars.items():
        if key == 'LANGSMITH_API_KEY' and value:
            display_value = f"***{value[-4:]}"
        else:
            display_value = value
        
        status = "✅" if value else "❌"
        print(f"  {status} {key}: {display_value}")
    
    # Check other important variables
    print("\n🔧 Other Configuration:")
    other_vars = {
        'ANTHROPIC_API_KEY': os.getenv('ANTHROPIC_API_KEY'),
        'DEFAULT_REPO_ID': os.getenv('DEFAULT_REPO_ID'),
        'GITHUB_APP_ID': os.getenv('GITHUB_APP_ID'),
    }
    
    for key, value in other_vars.items():
        if 'API_KEY' in key and value:
            display_value = f"***{value[-4:]}"
        else:
            display_value = value
        
        status = "✅" if value else "❌"
        print(f"  {status} {key}: {display_value}")
    
    # Test LangSmith import
    print("\n🧪 Testing LangSmith Import:")
    try:
        import langsmith
        print("✅ langsmith imported successfully")
        
        # Try to create client
        try:
            client = langsmith.Client()
            print(f"✅ LangSmith client created successfully")
            print(f"🔗 API URL: {client.api_url}")
        except Exception as e:
            print(f"⚠️  LangSmith client creation failed: {e}")
            
    except ImportError as e:
        print(f"❌ Failed to import langsmith: {e}")
    
    print("\n" + "=" * 50)
    
    # Summary
    # Check if either LANGSMITH_* or LANGCHAIN_* variables are properly set
    langsmith_set = all([
        os.getenv('LANGSMITH_TRACING'),
        os.getenv('LANGSMITH_ENDPOINT'),
        os.getenv('LANGSMITH_API_KEY'),
        os.getenv('LANGSMITH_PROJECT')
    ])

    langchain_set = all([
        os.getenv('LANGCHAIN_TRACING_V2'),
        os.getenv('LANGCHAIN_ENDPOINT'),
        os.getenv('LANGCHAIN_API_KEY'),
        os.getenv('LANGCHAIN_PROJECT')
    ])

    if langsmith_set or langchain_set:
        print("🎉 LangSmith/LangChain variables are set!")

        # Check if tracing is enabled (either variable set)
        tracing_enabled = (
            os.getenv('LANGSMITH_TRACING', '').lower() == 'true' or
            os.getenv('LANGCHAIN_TRACING_V2', '').lower() == 'true'
        )

        if tracing_enabled:
            print("🎯 Tracing is ENABLED - you should see traces in LangSmith!")
            if langsmith_set:
                print("📊 Using LANGSMITH_* environment variables")
            if langchain_set:
                print("📊 Using LANGCHAIN_* environment variables")
        else:
            print("⚠️  Tracing is DISABLED - set LANGSMITH_TRACING=true or LANGCHAIN_TRACING_V2=true to enable")
    else:
        print("❌ LangSmith/LangChain variables are missing - tracing won't work")
        print("💡 You need either LANGSMITH_* or LANGCHAIN_* variables set")

if __name__ == "__main__":
    main()
