#!/usr/bin/env python3
"""
Debug Claude Code execution to understand why it's failing.
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
from sandbox.modal_sandbox import create_sandbox

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

async def debug_claude_execution():
    """Debug Claude Code execution step by step."""
    print("🔍 Debugging Claude Code execution...")
    
    try:
        # Create sandbox
        print("🚀 Creating sandbox...")
        sandbox = await create_sandbox(repo_id="1", timeout=300, use_snapshot=True)
        print("✅ Sandbox created successfully")
        
        # Test basic commands first
        print("\n1️⃣ Testing basic bash commands...")
        process = sandbox.exec("bash", "-c", "echo 'Hello World'")
        stdout = process.stdout.read()
        process.wait()
        print(f"  Echo test: {stdout} (return code: {process.returncode})")
        
        # Test Claude Code availability
        print("\n2️⃣ Testing Claude Code availability...")
        process = sandbox.exec("bash", "-c", "which claude")
        stdout = process.stdout.read()
        process.wait()
        print(f"  Claude location: {stdout} (return code: {process.returncode})")
        
        # Test Claude Code version
        print("\n3️⃣ Testing Claude Code version...")
        process = sandbox.exec("bash", "-c", "claude --version")
        stdout = process.stdout.read()
        stderr = process.stderr.read()
        process.wait()
        print(f"  Version stdout: {stdout}")
        print(f"  Version stderr: {stderr}")
        print(f"  Return code: {process.returncode}")
        
        # Test Claude Code help
        print("\n4️⃣ Testing Claude Code help...")
        process = sandbox.exec("bash", "-c", "claude --help")
        stdout = process.stdout.read()
        stderr = process.stderr.read()
        process.wait()
        print(f"  Help stdout: {stdout[:500]}...")
        print(f"  Help stderr: {stderr[:200]}...")
        print(f"  Return code: {process.returncode}")
        
        # Test environment variables
        print("\n5️⃣ Testing environment variables...")
        process = sandbox.exec("bash", "-c", "env | grep -E '(ANTHROPIC|LANGSMITH)' | head -5")
        stdout = process.stdout.read()
        process.wait()
        print(f"  Environment vars: {stdout}")
        
        # Test simple Claude command (non-streaming)
        print("\n6️⃣ Testing simple Claude command...")
        simple_cmd = "echo 'What is 2+2?' | claude -p"
        process = sandbox.exec("bash", "-c", simple_cmd, timeout=30)
        stdout = process.stdout.read()
        stderr = process.stderr.read()
        process.wait()
        print(f"  Simple stdout: {stdout[:500]}...")
        print(f"  Simple stderr: {stderr[:200]}...")
        print(f"  Return code: {process.returncode}")
        
        # Test the exact command that's failing
        print("\n7️⃣ Testing exact failing command...")
        prompt = "List the files in the current directory using ls command"
        escaped_prompt = prompt.replace("'", "'\"'\"'")
        claude_command = "claude -p --output-format stream-json --verbose --allowedTools Edit,Write,MultiEdit,Read,Bash"
        full_command = f"cd /workspace && echo '{escaped_prompt}' | {claude_command}"
        
        print(f"  Command: {full_command}")
        process = sandbox.exec("bash", "-c", full_command, timeout=30)
        stdout = process.stdout.read()
        stderr = process.stderr.read()
        process.wait()
        print(f"  Exact stdout: {stdout[:1000]}...")
        print(f"  Exact stderr: {stderr[:500]}...")
        print(f"  Return code: {process.returncode}")
        
        # Test without stream-json
        print("\n8️⃣ Testing without stream-json...")
        simple_exact_cmd = f"cd /workspace && echo '{escaped_prompt}' | claude -p --verbose --allowedTools Edit,Write,MultiEdit,Read,Bash"
        print(f"  Command: {simple_exact_cmd}")
        process = sandbox.exec("bash", "-c", simple_exact_cmd, timeout=30)
        stdout = process.stdout.read()
        stderr = process.stderr.read()
        process.wait()
        print(f"  No-stream stdout: {stdout[:1000]}...")
        print(f"  No-stream stderr: {stderr[:500]}...")
        print(f"  Return code: {process.returncode}")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(debug_claude_execution())
    print(f"\n🎯 Debug {'completed' if success else 'failed'}")
