"""Project utilities for handling different package managers and monorepo setups."""

import logging
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any
from enum import Enum

logger = logging.getLogger(__name__)


class PackageManager(Enum):
    """Supported package managers."""
    NPM = "npm"
    YARN = "yarn"
    PNPM = "pnpm"
    BUN = "bun"


class ProjectType(Enum):
    """Common project types."""
    STANDALONE = "standalone"
    MONOREPO = "monorepo"
    TURBOREPO = "turborepo"
    NX = "nx"
    LERNA = "lerna"


def detect_package_manager(sandbox: Any) -> PackageManager:
    """Detect which package manager to use based on lockfiles and package.json.
    
    Args:
        sandbox: Modal sandbox instance
        
    Returns:
        PackageManager enum value
    """
    # Check for lockfiles first (most reliable)
    lockfile_mapping = {
        "pnpm-lock.yaml": PackageManager.PNPM,
        "yarn.lock": PackageManager.YARN,
        "package-lock.json": PackageManager.NPM,
        "bun.lockb": PackageManager.BUN,
    }
    
    for lockfile, pm in lockfile_mapping.items():
        check_cmd = f"test -f {lockfile} && echo 'exists'"
        check_process = sandbox.exec("bash", "-c", check_cmd)
        output = check_process.stdout.read().strip()
        check_process.wait()
        
        if output == "exists":
            logger.info(f"📦 Detected {pm.value} from {lockfile}")
            return pm
    
    # Check package.json for packageManager field (newer standard)
    pkg_manager_cmd = """
    if [ -f package.json ]; then
        cat package.json | grep -o '"packageManager"[[:space:]]*:[[:space:]]*"[^"]*"' | sed 's/.*"packageManager"[[:space:]]*:[[:space:]]*"\\([^@]*\\).*/\\1/'
    fi
    """
    pkg_process = sandbox.exec("bash", "-c", pkg_manager_cmd)
    pkg_output = pkg_process.stdout.read().strip()
    pkg_process.wait()
    
    if pkg_output:
        try:
            pm = PackageManager(pkg_output)
            logger.info(f"📦 Detected {pm.value} from package.json packageManager field")
            return pm
        except ValueError:
            logger.warning(f"⚠️ Unknown package manager in package.json: {pkg_output}")
    
    # Default to npm
    logger.info("📦 No specific package manager detected, defaulting to npm")
    return PackageManager.NPM


def detect_project_type(sandbox: Any) -> ProjectType:
    """Detect the type of project (monorepo, turborepo, etc).
    
    Args:
        sandbox: Modal sandbox instance
        
    Returns:
        ProjectType enum value
    """
    # Check for common monorepo config files
    config_checks = [
        ("turbo.json", ProjectType.TURBOREPO),
        ("nx.json", ProjectType.NX),
        ("lerna.json", ProjectType.LERNA),
        ("pnpm-workspace.yaml", ProjectType.MONOREPO),
        ("rush.json", ProjectType.MONOREPO),
    ]
    
    for config_file, project_type in config_checks:
        check_cmd = f"test -f {config_file} && echo 'exists'"
        check_process = sandbox.exec("bash", "-c", check_cmd)
        output = check_process.stdout.read().strip()
        check_process.wait()
        
        if output == "exists":
            logger.info(f"🏗️ Detected {project_type.value} from {config_file}")
            return project_type
    
    # Check for workspaces in package.json
    workspaces_cmd = """
    if [ -f package.json ]; then
        cat package.json | grep -q '"workspaces"' && echo 'has-workspaces'
    fi
    """
    ws_process = sandbox.exec("bash", "-c", workspaces_cmd)
    ws_output = ws_process.stdout.read().strip()
    ws_process.wait()
    
    if ws_output == "has-workspaces":
        logger.info("🏗️ Detected monorepo from package.json workspaces field")
        return ProjectType.MONOREPO
    
    logger.info("🏗️ Detected standalone project")
    return ProjectType.STANDALONE


def get_install_command(package_manager: PackageManager, flags: Optional[str] = None) -> str:
    """Get the install command for a package manager.
    
    Args:
        package_manager: The package manager to use
        flags: Optional flags to pass to the install command
        
    Returns:
        The install command string
    """
    base_commands = {
        PackageManager.NPM: "npm install",
        PackageManager.YARN: "yarn install",
        PackageManager.PNPM: "pnpm install",
        PackageManager.BUN: "bun install",
    }
    
    cmd = base_commands[package_manager]
    if flags:
        cmd += f" {flags}"
    
    return cmd


def setup_project_dependencies(sandbox: Any) -> Tuple[bool, str]:
    """Set up project dependencies with automatic package manager detection.
    
    Args:
        sandbox: Modal sandbox instance
        
    Returns:
        Tuple of (success, message)
    """
    try:
        # Detect package manager
        pm = detect_package_manager(sandbox)
        
        # Detect project type
        project_type = detect_project_type(sandbox)
        
        # Get appropriate install command
        install_flags = None
        if project_type == ProjectType.TURBOREPO and pm == PackageManager.NPM:
            # Turborepo with npm might need legacy peer deps flag
            install_flags = "--legacy-peer-deps"
        
        install_cmd = get_install_command(pm, install_flags)
        
        # Run installation
        logger.info(f"📦 Running {install_cmd} for {project_type.value} project...")
        install_process = sandbox.exec("bash", "-c", install_cmd)
        
        # Capture output for debugging
        stdout = install_process.stdout.read()
        stderr = install_process.stderr.read()
        install_process.wait()
        
        if install_process.returncode == 0:
            logger.info(f"✅ {install_cmd} completed successfully")
            return True, f"Successfully installed dependencies with {pm.value}"
        else:
            error_msg = stderr or stdout
            logger.warning(f"⚠️ {install_cmd} had issues: {error_msg}")
            
            # Try to provide helpful suggestions
            if "WARN" in error_msg and install_process.returncode == 0:
                # Some warnings are OK
                return True, f"Installed with warnings using {pm.value}"
            elif "permission denied" in error_msg.lower():
                return False, "Permission denied - may need to adjust sandbox permissions"
            elif "not found" in error_msg.lower():
                return False, f"{pm.value} not available in sandbox"
            else:
                return False, f"Installation failed: {error_msg[:200]}"
                
    except Exception as e:
        logger.error(f"❌ Failed to setup dependencies: {e}")
        return False, f"Setup failed: {str(e)}"


def get_project_info(sandbox: Any) -> Dict[str, Any]:
    """Get comprehensive project information.
    
    Args:
        sandbox: Modal sandbox instance
        
    Returns:
        Dictionary with project information
    """
    pm = detect_package_manager(sandbox)
    project_type = detect_project_type(sandbox)
    
    # Check for common framework files
    frameworks = []
    framework_checks = [
        ("next.config.js", "Next.js"),
        ("next.config.mjs", "Next.js"),
        ("gatsby-config.js", "Gatsby"),
        ("vue.config.js", "Vue"),
        ("angular.json", "Angular"),
        ("svelte.config.js", "Svelte"),
        ("remix.config.js", "Remix"),
        ("astro.config.mjs", "Astro"),
    ]
    
    for config_file, framework in framework_checks:
        check_cmd = f"test -f {config_file} && echo 'exists'"
        check_process = sandbox.exec("bash", "-c", check_cmd)
        if check_process.stdout.read().strip() == "exists":
            frameworks.append(framework)
    
    # Get workspace info if monorepo
    workspaces = []
    if project_type != ProjectType.STANDALONE:
        # Try to list workspace directories
        ws_cmd = """
        if [ -f pnpm-workspace.yaml ]; then
            # Parse pnpm workspaces
            cat pnpm-workspace.yaml | grep -E '^ *- ' | sed 's/^ *- *//' | tr -d "'\"" | head -10
        elif [ -f package.json ] && grep -q '"workspaces"' package.json; then
            # Try to parse npm/yarn workspaces (simplified)
            echo "apps/*"
            echo "packages/*"
        fi
        """
        ws_process = sandbox.exec("bash", "-c", ws_cmd)
        ws_output = ws_process.stdout.read().strip()
        if ws_output:
            workspaces = ws_output.split('\n')
    
    return {
        "package_manager": pm.value,
        "project_type": project_type.value,
        "frameworks": frameworks,
        "workspaces": workspaces,
        "is_monorepo": project_type != ProjectType.STANDALONE,
    }