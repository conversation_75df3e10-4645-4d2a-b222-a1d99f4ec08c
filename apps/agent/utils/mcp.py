"""MCP (Model Context Protocol) utilities for configuring and managing MCP servers."""

import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


def configure_mcp_servers(sandbox: Any, smithery_key: Optional[str] = None) -> bool:
    """Configure MCP servers in the sandbox before creating a snapshot.
    
    Args:
        sandbox: Modal sandbox instance
        smithery_key: Optional Smithery API key for context7 server
        
    Returns:
        True if configuration was successful, False otherwise
    """
    try:
        # Build MCP configuration
        mcp_config = {
            "mcpServers": {
                "puppeteer": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-puppeteer"]
                }
            }
        }
        
        # Add context7 only if we have a key
        if smithery_key:
            mcp_config["mcpServers"]["context7"] = {
                "command": "npx",
                "args": ["-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", smithery_key]
            }
            logger.info("🔑 Including context7 MCP server with provided key")
        else:
            logger.info("⚠️ No Smithery key provided, skipping context7 MCP server")
        
        # Create the configuration script
        # Also create in /etc/claude for persistence across snapshots
        config_json = json.dumps(mcp_config, indent=2)
        mcp_script = f"""
# Create in home directory for current session
mkdir -p ~/.config/claude
cat > ~/.config/claude/claude_cli_config.json << 'EOF'
{config_json}
EOF

# Also create in /etc for persistence in snapshots
mkdir -p /etc/claude
cat > /etc/claude/claude_cli_config.json << 'EOF'
{config_json}
EOF

# Create a symlink if needed
if [ ! -f ~/.config/claude/claude_cli_config.json ]; then
    ln -sf /etc/claude/claude_cli_config.json ~/.config/claude/claude_cli_config.json
fi

echo "✅ MCP servers configured successfully"
"""
        
        # Execute the configuration
        logger.info("🔧 Writing MCP configuration...")
        mcp_process = sandbox.exec("bash", "-c", mcp_script)
        output = mcp_process.stdout.read()
        stderr = mcp_process.stderr.read()
        mcp_process.wait()
        
        if mcp_process.returncode != 0:
            logger.error(f"❌ Failed to configure MCP servers: {stderr}")
            return False
            
        logger.info(f"MCP config output: {output}")
        
        # Verify the configuration was written
        verify_process = sandbox.exec("bash", "-c", "cat ~/.config/claude/claude_cli_config.json")
        config_content = verify_process.stdout.read()
        verify_process.wait()
        
        if config_content:
            logger.info("✅ MCP configuration verified")
            # List configured servers
            config_data = json.loads(config_content)
            servers = list(config_data.get("mcpServers", {}).keys())
            logger.info(f"📋 MCP servers configured: {', '.join(servers)}")
            return True
        else:
            logger.error("❌ Failed to verify MCP configuration")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to configure MCP servers: {e}")
        return False


def verify_claude_code_with_mcp(sandbox: Any) -> bool:
    """Verify Claude Code is installed and MCP servers are configured.
    
    Args:
        sandbox: Modal sandbox instance
        
    Returns:
        True if Claude Code and MCP are properly configured
    """
    try:
        # Check Claude Code installation
        claude_process = sandbox.exec("bash", "-c", "claude --version")
        claude_output = claude_process.stdout.read()
        claude_process.wait()
        
        if claude_process.returncode != 0:
            logger.error("❌ Claude Code not found")
            return False
            
        logger.info(f"✅ Claude Code version: {claude_output.strip()}")
        
        # Check MCP configuration (try multiple locations)
        config_check_cmd = """
        if [ -f ~/.config/claude/claude_cli_config.json ]; then
            echo 'exists-home'
        elif [ -f /etc/claude/claude_cli_config.json ]; then
            echo 'exists-etc'
            # Create symlink if needed
            mkdir -p ~/.config/claude
            ln -sf /etc/claude/claude_cli_config.json ~/.config/claude/claude_cli_config.json
            echo 'linked'
        else
            echo 'not-found'
        fi
        """
        config_process = sandbox.exec("bash", "-c", config_check_cmd)
        config_result = config_process.stdout.read().strip()
        config_process.wait()
        
        if "not-found" in config_result:
            logger.error("❌ MCP configuration file not found in any location")
            return False
        
        if "linked" in config_result:
            logger.info("🔗 Created symlink from /etc/claude to ~/.config/claude")
            
        logger.info("✅ MCP configuration file exists")
        
        # Read and display MCP servers
        servers_process = sandbox.exec("bash", "-c", 
            "cat ~/.config/claude/claude_cli_config.json | jq -r '.mcpServers | keys[]' 2>/dev/null || echo 'No servers'")
        servers = servers_process.stdout.read().strip()
        servers_process.wait()
        
        if servers and servers != "No servers":
            logger.info(f"📋 MCP servers available: {servers.replace('\\n', ', ')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to verify Claude Code with MCP: {e}")
        return False