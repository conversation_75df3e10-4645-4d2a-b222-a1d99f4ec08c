#!/usr/bin/env python3
"""
Test the UTC timezone timing fix for LangSmith RunTree.
"""

import os
import time
import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_utc_timing_fix():
    """Test that UTC timezone-aware datetime objects work correctly with LangSmith."""
    print("🧪 Testing UTC Timezone Timing Fix")
    print("=" * 50)
    
    try:
        from langsmith.run_trees import RunTree
        
        project_name = os.getenv("LANGSMITH_PROJECT", "backspace-testing")
        
        # Create a parent run
        parent_run = RunTree(
            name="🧪 UTC Timing Test",
            run_type="chain",
            inputs={"test": "UTC timezone timing"},
            project_name=project_name,
            extra={"description": "Testing UTC timezone-aware datetime timing"}
        )
        parent_run.post()
        print("✅ Parent run created and posted")
        
        # Create a child tool run with manual timing
        start_timestamp = time.time()
        print(f"📊 Tool start timestamp: {start_timestamp}")
        
        tool_run = parent_run.create_child(
            name="🔧 Tool: TestTool",
            run_type="tool",
            inputs={"command": "test command", "timestamp": start_timestamp}
        )
        
        # Set start time with UTC timezone (like our fix)
        tool_run.start_time = datetime.datetime.fromtimestamp(start_timestamp, tz=datetime.timezone.utc)
        tool_run.post()
        print(f"✅ Tool run created with UTC start time: {tool_run.start_time}")
        
        # Simulate some work (2 seconds)
        print("⏳ Simulating 2-second tool execution...")
        time.sleep(2)
        
        # End the tool with UTC timezone
        end_timestamp = time.time()
        tool_run.end_time = datetime.datetime.fromtimestamp(end_timestamp, tz=datetime.timezone.utc)
        
        # Calculate duration
        actual_duration_ms = int((end_timestamp - start_timestamp) * 1000)
        
        tool_run.outputs = {
            "result": "Test tool completed successfully",
            "status": "✅",
            "actual_duration_ms": actual_duration_ms,
            "start_timestamp": start_timestamp,
            "end_timestamp": end_timestamp
        }
        tool_run.name += f" ✅ ({actual_duration_ms}ms)"
        
        print(f"📊 Tool timing:")
        print(f"   Start: {tool_run.start_time}")
        print(f"   End: {tool_run.end_time}")
        print(f"   Duration: {actual_duration_ms}ms")
        print(f"   Timezone aware: {tool_run.start_time.tzinfo is not None}")
        
        tool_run.patch()
        print("✅ Tool run ended and patched")
        
        # End parent
        parent_run.end(outputs={
            "success": True,
            "tools_executed": 1,
            "total_duration_ms": actual_duration_ms
        })
        parent_run.patch()
        print("✅ Parent run ended and patched")
        
        print(f"\n🎯 Test completed! Check LangSmith project '{project_name}' for timing accuracy.")
        print(f"   Expected duration: ~{actual_duration_ms}ms (should show in LangSmith UI)")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_utc_timing_fix()
    if success:
        print("\n✅ UTC timing fix test completed successfully!")
        print("🔍 Check your LangSmith dashboard to verify timing is now accurate")
    else:
        print("\n❌ UTC timing fix test failed!")
