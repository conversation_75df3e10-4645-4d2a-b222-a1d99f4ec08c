"""Modal sandbox utilities for Claude Code execution."""

import json
import logging
import os
import asyncio
import subprocess
from typing import Optional, Any, Dict, List, Callable
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv

import modal

# Lang<PERSON>mith imports for tracing
try:
    from langsmith import Client
    from langsmith.run_trees import RunTree
    LANGSMITH_AVAILABLE = True
except ImportError:
    LANGSMITH_AVAILABLE = False
    Client = RunTree = None

# from utils.github_auth import create_installation_token
from data.convex_client import convex_client
from utils.mcp import configure_mcp_servers
from sandbox.image import claude_dev_image

# Configure logger with emoji support
logger = logging.getLogger(__name__)
load_dotenv()


@dataclass
class ClaudeOutput:
    """Container for storing Claude Code outputs."""

    timestamp: float
    type: str
    content: Any
    raw_event: Optional[Dict[str, Any]] = None

    def __str__(self):
        return f"[{self.timestamp:.2f}] {self.type}: {self.content}"


@dataclass
class ClaudeSession:
    """Container for a complete Claude Code session."""

    session_id: str
    prompt: str
    outputs: List[ClaudeOutput] = field(default_factory=list)
    start_time: float = field(default_factory=lambda: datetime.now().timestamp())
    end_time: Optional[float] = None
    total_cost_usd: Optional[float] = None
    duration_ms: Optional[int] = None
    success: bool = False
    error: Optional[str] = None

    # LangSmith tracing
    trace_enabled: bool = False
    run_tree: Optional[Any] = field(default=None, init=False)
    child_runs: List[Any] = field(default_factory=list, init=False)
    pending_tools: Dict[str, Any] = field(default_factory=dict, init=False)

    def init_tracing(self, parent_run: Optional[Any] = None):
        """Initialize LangSmith tracing for this session."""
        logger.info(f"🔍 init_tracing called: LANGSMITH_AVAILABLE={LANGSMITH_AVAILABLE}, trace_enabled={self.trace_enabled}")
        if not (LANGSMITH_AVAILABLE and self.trace_enabled):
            return
        if self.run_tree is not None:
            return

        try:
            # Check for both LANGSMITH_* and LANGCHAIN_* environment variables
            api_key = os.getenv("LANGSMITH_API_KEY") or os.getenv("LANGCHAIN_API_KEY")
            tracing_enabled = (
                os.getenv("LANGSMITH_TRACING", "false").lower() == "true" or
                os.getenv("LANGCHAIN_TRACING_V2", "false").lower() == "true"
            )
            project_name = (
                os.getenv("LANGSMITH_PROJECT") or
                os.getenv("LANGCHAIN_PROJECT") or
                "claude-modal-tracing"
            )

            if not (api_key and tracing_enabled):
                logger.warning("⚠️ LangSmith tracing disabled (env not set)")
                return

            # If we have a parent run, use it directly for tool tracing; otherwise create root
            if parent_run:
                logger.info(f"🎯 Using LangGraph node directly for tool tracing (no wrapper)")
                self.run_tree = parent_run
                self._using_parent_run = True  # Flag to avoid finalizing parent run
                # Don't post since parent is already posted
            else:
                # Fallback to root run if no parent
                logger.info(f"⚠️ No parent run provided, creating root run")
                self.run_tree = RunTree(
                    name="🤖 Claude Code Session (Modal)",
                    run_type="chain",
                    inputs={
                        "prompt": self.prompt[:200] + "..." if len(self.prompt) > 200 else self.prompt,
                        "session_id": self.session_id,
                        "sandbox_type": "modal"
                    },
                    project_name=project_name,
                    extra={
                        "description": "Claude Code execution in Modal sandbox with real-time tracing"
                    }
                )
                # Post the run tree only if we created it
                self.run_tree.post()

            logger.info(f"✅ LangSmith tracing initialized for {self.session_id}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to init LangSmith tracing: {e}")
            logger.debug(f"Tracing error details: {type(e).__name__}: {e}")
            import traceback
            logger.debug(f"Traceback: {traceback.format_exc()}")
            self.trace_enabled = False

    def add_output(self, output: ClaudeOutput):
        """Add an output to the session."""
        self.outputs.append(output)
        if self.trace_enabled:
            self._trace_output(output)

    def _trace_output(self, output: ClaudeOutput):
        """Trace an output to LangSmith."""
        if not self.run_tree:
            return

        try:
            # Special handling for tool_call / tool_result (nesting)
            if output.type == "tool_call" and isinstance(output.content, dict):
                tool_id = output.content.get("id")
                tool_name = output.content.get('name', 'Unknown')



                # Create child with accurate start_time using UTC timezone
                tool_run = self.run_tree.create_child(
                    name=f"🔧 Tool: {tool_name}",
                    run_type="tool",
                    inputs={**output.content, "timestamp": output.timestamp},
                    extra={"raw_event": output.raw_event},
                    start_time=datetime.fromtimestamp(output.timestamp, tz=timezone.utc)
                )
                tool_run.post()  # Start the run, but keep it open

                logger.info(f"🔧 TOOL_CALL: {tool_name} (ID: {tool_id})")

                # Store the run - we'll end() it when tool_result arrives
                self.pending_tools[tool_id] = {
                    "run": tool_run,
                    "start_timestamp": output.timestamp,
                    "tool_name": tool_name
                }
                self.child_runs.append(tool_run)

                logger.info(f"   Stored in pending_tools. New count: {len(self.pending_tools)}")
                return

            if output.type == "tool_result" and isinstance(output.content, dict):
                # Try both possible keys for tool matching
                tool_id = output.content.get("tool_use_id") or output.content.get("id")

                logger.info(f"🔧 TOOL_RESULT: {tool_id}")



                tool_info = self.pending_tools.pop(tool_id, None)



                if tool_info and isinstance(tool_info, dict):
                    parent_tool = tool_info["run"]
                    start_timestamp = tool_info["start_timestamp"]
                    tool_name = tool_info.get("tool_name", "Unknown")

                    # Calculate actual duration based on timestamps
                    actual_duration_ms = int((output.timestamp - start_timestamp) * 1000)

                    logger.info(f"🕐 Tool {tool_name} completed in {actual_duration_ms}ms")

                    # Update tool name with status only
                    parent_tool.name += (
                        f" {'❌' if output.content.get('is_error') else '✅'}"
                    )

                    # Set the end_time attribute directly on the RunTree object
                    end_datetime = datetime.fromtimestamp(output.timestamp, tz=timezone.utc)
                    parent_tool.end_time = end_datetime

                    parent_tool.end(
                        outputs={
                            **output.content,
                            "status": "❌" if output.content.get("is_error") else "✅",
                            "actual_duration_ms": actual_duration_ms
                        },
                        end_time=end_datetime
                    )

                    parent_tool.patch()
                    parent_tool.wait()  # Wait for all futures to complete

                return

            # Generic events
            event_configs = {
                "system": ("🚀 System Init", "chain"),
                "claude_message": ("🤖 Claude Message", "llm"),
                "thinking": ("🧠 Claude Thinking", "llm"),
                "error": ("💥 Error", "chain"),
                "result": ("📊 Final Result", "chain"),
            }

            config = event_configs.get(output.type, (f"📝 {output.type}", "chain"))

            child = self.run_tree.create_child(
                name=config[0],
                run_type=config[1],
                inputs={"type": output.type, "timestamp": output.timestamp},
                outputs={"content": output.content},
                extra={"raw_event": output.raw_event}
            )
            child.post()
            child.end()
            child.patch()
            self.child_runs.append(child)
        except Exception as e:
            logger.warning(f"⚠️ Failed to trace output: {e}")

    def finalize(self, success: bool = True, error: Optional[str] = None):
        """Mark the session as complete."""
        self.end_time = datetime.now().timestamp()
        self.success = success
        self.error = error
        self.duration_ms = int((self.end_time - self.start_time) * 1000)

        # Only finalize the run tree if we created it ourselves (not using parent run)
        if self.trace_enabled and self.run_tree and not hasattr(self, '_using_parent_run'):
            try:
                self.run_tree.end(outputs={
                    "success": self.success,
                    "error": self.error,
                    "duration_ms": self.duration_ms,
                    "total_cost_usd": self.total_cost_usd,
                    "total_outputs": len(self.outputs),
                    "child_runs_created": len(self.child_runs),
                })
                self.run_tree.patch()
            except Exception as e:
                logger.warning(f"⚠️ finalize_trace failed: {e}")

    @property
    def elapsed_time(self) -> float:
        """Get elapsed time in seconds."""
        if self.end_time:
            return self.end_time - self.start_time
        return datetime.now().timestamp() - self.start_time


# GitHub App authentication implementation
async def create_installation_token(installation_id: int) -> str:
    """Generate installation access token using gh CLI.

    Args:
        installation_id: GitHub App installation ID

    Returns:
        Installation access token (valid for 1 hour)

    Raises:
        ValueError: If required environment variables are missing
        Exception: If token generation fails
    """
    # Get credentials from environment
    app_id = os.getenv("GITHUB_APP_ID")
    private_key = os.getenv("GITHUB_PRIVATE_KEY")

    if not app_id or not private_key:
        raise ValueError(
            "GITHUB_APP_ID and GITHUB_PRIVATE_KEY environment variables are required"
        )

    logger.info(f"Generating installation token for installation {installation_id}")

    try:
        # Generate token using gh CLI - wrap in thread to avoid blocking
        def _run_gh_command():
            import subprocess

            return subprocess.run(
                [
                    "gh",
                    "token",
                    "generate",
                    "--base64-key",
                    private_key,
                    "--app-id",
                    str(app_id),
                    "--installation-id",
                    str(installation_id),
                ],
                capture_output=True,
                text=True,
                check=True,
            )

        result = await asyncio.to_thread(_run_gh_command)

        # Parse JSON response
        token_data = json.loads(result.stdout)

        logger.info(f"✅ Generated token for installation {installation_id}")
        return token_data["token"]

    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to generate token: {e.stderr}")
        raise Exception(f"Failed to generate installation token: {e.stderr}")
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse token response: {e}")
        raise Exception(f"Invalid token response format: {e}")




async def get_installation_id_from_repo(repo_id: str) -> tuple[int, str]:
    """Get GitHub installation ID and repository URL from repository ID via data client.

    Args:
        repo_id: Repository ID to lookup

    Returns:
        Tuple of (GitHub installation ID, repository URL)

    Raises:
        ValueError: If repo_id not found or missing installation_id
    """
    try:
        return await convex_client.get_installation_id_and_repo_url(repo_id)

    except Exception as e:
        logger.error(
            f"Failed to get installation_id and repo_url for repo {repo_id}: {e}"
        )
        raise




def _extract_snapshot_id(image: modal.Image) -> Optional[str]:
    """Extract snapshot ID from Modal Image using multiple methods.

    Args:
        image: Modal Image object

    Returns:
        Snapshot ID string if found, None otherwise
    """
    # Method 1: Check for 'id' attribute
    if hasattr(image, "id") and image.id:
        return image.id

    # Method 2: Check for 'object_id' attribute
    if hasattr(image, "object_id") and image.object_id:
        return image.object_id

    # Method 3: Check string representation
    if hasattr(image, "__str__"):
        str_repr = str(image)
        # Try to extract ID from string like "Image.from_id('im-xyz')"
        if "from_id('" in str_repr:
            start = str_repr.find("from_id('") + 9
            end = str_repr.find("')", start)
            if end > start:
                snapshot_id = str_repr[start:end]
                return snapshot_id

    # Method 4: Log warning if no ID found
    logger.warning(f"⚠️ Could not extract snapshot ID from: {type(image)}")

    return None


async def create_repo_snapshot(
    repo_id: str, github_token: Optional[str] = None
) -> modal.Image:
    """Create or retrieve a cached snapshot for a repository.

    Args:
        repo_id: Repository ID to create snapshot for
        github_token: GitHub token (if None, will be generated from repo_id)

    Returns:
        Modal Image snapshot ready for use
    """
    # Always create fresh snapshot - no caching since we store in DB

    logger.info(f"📸 Creating new snapshot for repo {repo_id}")

    try:
        # Get repo info if github_token not provided
        if not github_token:
            installation_id, repo_url = await get_installation_id_from_repo(repo_id)
            github_token = await create_installation_token(
                installation_id=installation_id
            )
        else:
            # Get repo URL from client
            repo = convex_client.get_repository_by_id(repo_id)
            if not repo:
                raise ValueError(f"No repository found for repo_id {repo_id}")
            repo_url = repo.get("url")
            if not repo_url:
                raise ValueError(f"No repository URL found for repo_id {repo_id}")

        repo_path = repo_url.replace("https://github.com/", "")
        logger.info(f"🔗 Repository: {repo_url}")

        # Create a temporary sandbox to set up the repo
        app = modal.App.lookup("backspace", create_if_missing=True)

        # Create secret for GitHub token
        env_secret = modal.Secret.from_dict(
            {
                "GH_TOKEN": github_token,
                "SMITHERLY_KEY": os.getenv("SMITHERLY_KEY"),
                "WORKSPACE_PATH": "/workspace",
            }
        )

        # Create sandbox with our custom image using async method
        sandbox = await modal.Sandbox.create.aio(
            image=claude_dev_image, app=app, secrets=[env_secret]
        )

        try:
            # Clone the repository
            logger.info("📂 Cloning repository...")
            clone_cmd = f"git clone https://x-access-token:{github_token}@github.com/{repo_path} ."
            clone_process = sandbox.exec("bash", "-c", clone_cmd)
            clone_process.stdout.read()
            clone_process.wait()  # Wait for process to complete

            if clone_process.returncode != 0:
                error_msg = clone_process.stderr.read()
                raise Exception(f"Failed to clone repository: {error_msg}")

            logger.info("✅ Repository cloned successfully")

            # Configure git
            git_config_cmd = "git config user.email '<EMAIL>' && git config user.name 'Backspace Agent'"
            config_process = sandbox.exec("bash", "-c", git_config_cmd)
            config_process.stdout.read()  # Wait for completion

            # Setup package dependencies
            await _setup_project_dependencies(sandbox)
            
            # Get Smithery key from environment if available
            smithery_key = os.getenv("SMITHERLY_KEY")
            if not configure_mcp_servers(sandbox, smithery_key):
                logger.warning("⚠️ MCP configuration failed, continuing anyway...")

            # List workspace contents for verification
            ls_process = sandbox.exec("bash", "-c", "ls -la")
            ls_output = ls_process.stdout.read()
            logger.info(f"📋 Workspace contents:\n{ls_output}")

            # Create the snapshot with MCP already configured
            logger.info("📸 Creating filesystem snapshot with dependencies and MCP configured...")
            snapshot_image = sandbox.snapshot_filesystem()

            # Log the snapshot ID using robust ID extraction
            snapshot_id = _extract_snapshot_id(snapshot_image)
            if snapshot_id:
                logger.info(f"📸 Snapshot created with ID: {snapshot_id}")

                # Update repository record with new snapshot ID
                try:
                    await convex_client.update_repository_snapshot_id(repo_id, snapshot_id)
                    logger.info(
                        f"✅ Updated repository {repo_id} with snapshot ID: {snapshot_id}"
                    )
                except Exception as e:
                    logger.warning(f"⚠️ Failed to update repository snapshot ID: {e}")
            else:
                logger.warning("⚠️ Could not retrieve snapshot ID")


            logger.info(f"✅ Snapshot created for repository {repo_id}")
            return snapshot_image

        finally:
            # Clean up the sandbox
            sandbox.terminate()

    except Exception as e:
        logger.error(f"❌ Failed to create snapshot for repo {repo_id}: {e}")
        raise


async def _setup_project_dependencies(sandbox: modal.Sandbox) -> None:
    """Set up project dependencies with automatic package manager detection."""
    from utils.project import setup_project_dependencies, get_project_info
    
    # Get project info for logging
    project_info = get_project_info(sandbox)
    logger.info(f"📊 Project info: {project_info}")
    
    # Run the setup
    success, message = setup_project_dependencies(sandbox)
    
    if not success:
        logger.error(f"❌ Dependency setup failed: {message}")
        # Continue anyway - some projects might work without full install
    else:
        logger.info(f"✅ {message}")


async def _create_new_snapshot(repo_id: str, github_token: str) -> modal.Image:
    """Create a new snapshot, falling back to base image on failure."""
    try:
        image = await asyncio.wait_for(
            create_repo_snapshot(
                repo_id=repo_id,
                github_token=github_token,
            ),
            timeout=300,  # 5 minute timeout
        )
        logger.info("✅ Created new snapshot")

        # Log and update snapshot ID for the new snapshot
        snapshot_id = _extract_snapshot_id(image)
        if snapshot_id:
            logger.info(f"📸 New snapshot ID: {snapshot_id}")

        return image
    except Exception as e:
        logger.warning(f"⚠️ Failed to create snapshot: {e}, using base image")
        return claude_dev_image


async def create_sandbox(
    repo_id: Optional[str] = None,
    envs: Optional[Dict[str, str]] = None,
    timeout: int = 300,
    use_snapshot: bool = True,
    parent_run: Optional[Any] = None,
) -> modal.Sandbox:
    """Create and return a Modal sandbox with Claude Code and fresh GitHub credentials.

    Args:
        repo_id: Repository ID to get GitHub installation_id from database. If None, uses env DEFAULT_REPO_ID.
        envs: Additional environment variables to set in the sandbox
        timeout: Sandbox creation timeout in seconds (default: 300s/5min)
        use_snapshot: Whether to use snapshot for faster setup (default: True)
        parent_run: Optional parent run for LangSmith tracing

    Returns:
        Modal Sandbox instance

    Raises:
        Exception: If sandbox creation or configuration fails
    """
    # Initialize tracing for create_sandbox
    run_tree = None
    if LANGSMITH_AVAILABLE and parent_run:
        try:
            from langsmith import run_trees
            run_tree = run_trees.RunTree(
                name="create_sandbox",
                run_type="tool",
                parent_run=parent_run,
                inputs={
                    "repo_id": repo_id,
                    "timeout": timeout,
                    "use_snapshot": use_snapshot,
                    "envs_count": len(envs) if envs else 0
                }
            )
            run_tree.post()
            logger.info(f"🔍 Started create_sandbox trace")
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize create_sandbox tracing: {e}")
            run_tree = None

    try:
        logger.info(f"🚀 Creating Modal sandbox...")

        # Determine effective repo_id
        effective_repo_id = repo_id or os.environ.get("DEFAULT_REPO_ID")
        if not effective_repo_id:
            raise ValueError(
                "No repo_id provided and DEFAULT_REPO_ID not set in environment"
            )

        # Prepare environment variables
        env_vars = {
            "ANTHROPIC_API_KEY": os.getenv("ANTHROPIC_API_KEY"),
            "SMITHERLY_KEY": os.getenv("SMITHERLY_KEY"),
            "WORKSPACE_PATH": "/workspace",
            # Pass through LangSmith tracing environment variables
            "LANGSMITH_TRACING": os.getenv("LANGSMITH_TRACING"),
            "LANGSMITH_ENDPOINT": os.getenv("LANGSMITH_ENDPOINT"),
            "LANGSMITH_API_KEY": os.getenv("LANGSMITH_API_KEY"),
            "LANGSMITH_PROJECT": os.getenv("LANGSMITH_PROJECT"),
            # Also support legacy LANGCHAIN_* variables
            "LANGCHAIN_TRACING_V2": os.getenv("LANGCHAIN_TRACING_V2"),
            "LANGCHAIN_ENDPOINT": os.getenv("LANGCHAIN_ENDPOINT"),
            "LANGCHAIN_API_KEY": os.getenv("LANGCHAIN_API_KEY"),
            "LANGCHAIN_PROJECT": os.getenv("LANGCHAIN_PROJECT"),
        }

        # Remove None values to avoid passing empty env vars
        env_vars = {k: v for k, v in env_vars.items() if v is not None}

        # Generate fresh GitHub token with tracing
        github_auth_run = None
        if run_tree:
            try:
                github_auth_run = run_tree.create_child(
                    name="🔑 GitHub Authentication",
                    run_type="tool",
                    inputs={"repo_id": effective_repo_id}
                )
                github_auth_run.post()
            except Exception as e:
                logger.debug(f"Failed to create GitHub auth trace: {e}")

        installation_id, repo_url = await get_installation_id_from_repo(
            effective_repo_id
        )
        fresh_github_token = await create_installation_token(
            installation_id=installation_id
        )
        env_vars["GH_TOKEN"] = fresh_github_token
        logger.debug("Generated fresh GitHub token")

        # Complete GitHub auth tracing
        if github_auth_run:
            try:
                github_auth_run.outputs = {
                    "installation_id": installation_id,
                    "repo_url": repo_url,
                    "token_generated": True
                }
                github_auth_run.end()
                github_auth_run.patch()
            except Exception as e:
                logger.debug(f"Failed to complete GitHub auth trace: {e}")

        # Merge with any additional envs provided
        if envs:
            env_vars.update(envs)
            logger.debug(f"📦 Added {len(envs)} additional environment variables")

        # Get or create the app
        app = modal.App.lookup("backspace", create_if_missing=True)

        # Determine which image to use - simplified logic
        image = claude_dev_image  # Default fallback

        # Snapshot operations with tracing
        snapshot_run = None
        if run_tree:
            try:
                snapshot_run = run_tree.create_child(
                    name="📸 Snapshot Operations",
                    run_type="tool",
                    inputs={"use_snapshot": use_snapshot, "repo_id": effective_repo_id}
                )
                snapshot_run.post()
            except Exception as e:
                logger.debug(f"Failed to create snapshot trace: {e}")

        if use_snapshot and effective_repo_id:
            try:
<<<<<<< Updated upstream
                # Try to get existing snapshot ID from database
=======
                # Try to get existing snapshot from cache first
                snapshot_key = _get_snapshot_key(effective_repo_id)
                if snapshot_key in _snapshot_cache:
                    logger.info(f"📸 Using cached snapshot for {effective_repo_id}")
                    image = _snapshot_cache[snapshot_key]
                    snapshot_result = "cached"
                else:
>>>>>>> Stashed changes
                    # Try to get existing snapshot ID from database
                    repo_data = convex_client.get_repository_by_id(effective_repo_id)
                    existing_snapshot_id = (
                        repo_data.get("modal_snapshot_id") if repo_data else None
                    )

                    if existing_snapshot_id and existing_snapshot_id.strip():
                        logger.info(
                            f"📸 Trying existing snapshot: {existing_snapshot_id}"
                        )
                        try:
                            image = modal.Image.from_id(existing_snapshot_id)
                            logger.info("✅ Using existing snapshot")
                            snapshot_result = "existing"
                        except Exception as e:
                            logger.warning(f"❌ Existing snapshot failed: {e}")
                            # Fall through to create new snapshot
                            image = await _create_new_snapshot(
                                effective_repo_id, fresh_github_token
                            )
                            snapshot_result = "created_new"
                    else:
                        # No existing snapshot, create new one
                        logger.info("📸 Creating new snapshot...")
                        image = await _create_new_snapshot(
                            effective_repo_id, fresh_github_token
                        )
                        snapshot_result = "created_new"

            except Exception as e:
                logger.warning(f"⚠️ Snapshot operations failed: {e}, using base image")
                image = claude_dev_image
                snapshot_result = "failed_fallback"
        else:
            snapshot_result = "disabled"

        # Complete snapshot tracing
        if snapshot_run:
            try:
                snapshot_run.outputs = {
                    "result": snapshot_result,
                    "image_type": "snapshot" if image != claude_dev_image else "base"
                }
                snapshot_run.end()
                snapshot_run.patch()
            except Exception as e:
                logger.debug(f"Failed to complete snapshot trace: {e}")

        # Initialize sandbox with tracing
        init_run = None
        if run_tree:
            try:
                init_run = run_tree.create_child(
                    name="📦 Initialize Sandbox",
                    run_type="tool",
                    inputs={"timeout": timeout, "image_type": "snapshot" if image != claude_dev_image else "base"}
                )
                init_run.post()
            except Exception as e:
                logger.debug(f"Failed to create init trace: {e}")

        # Create secrets for environment variables
        secrets_list = []
        if env_vars:
            env_secret = modal.Secret.from_dict(env_vars)
            secrets_list.append(env_secret)

        # Create the sandbox
        logger.info(f"📦 Initializing Modal sandbox (timeout: {timeout}s)...")
        logger.info(f"🖼️  Using image: {image} (ID: {getattr(image, 'id', 'N/A')})")

        try:
            sandbox = await modal.Sandbox.create.aio(
                image=image, app=app, secrets=secrets_list, timeout=timeout, verbose=True
            )
            init_result = "success"
        except Exception as e:
            # If sandbox creation fails with snapshot image, create new sandbox with base image
            if image != claude_dev_image:
                logger.warning(f"⚠️ Sandbox creation failed with snapshot image: {e}")
                logger.info("🔄 Creating new sandbox with base image...")
                image = claude_dev_image
                sandbox = await modal.Sandbox.create.aio(
                    image=image, app=app, secrets=secrets_list, timeout=timeout
                )
                # Set flag to setup repository manually since we're not using snapshot
                use_snapshot = False
                init_result = "fallback_success"
            else:
                init_result = "failed"
                raise

        logger.info(f"✅ Successfully created Modal sandbox")

        # Complete init tracing
        if init_run:
            try:
                init_run.outputs = {
                    "result": init_result,
                    "sandbox_id": str(sandbox.object_id) if hasattr(sandbox, 'object_id') else "unknown"
                }
                init_run.end()
                init_run.patch()
            except Exception as e:
                logger.debug(f"Failed to complete init trace: {e}")

        # If not using snapshot, set up the repository manually
        if not use_snapshot and effective_repo_id:
            await _setup_repository_in_sandbox(
                sandbox, effective_repo_id, fresh_github_token, repo_url
            )

            # Create a new snapshot after manual setup for future use
            try:
                logger.info("📸 Creating new snapshot after manual setup...")
                new_snapshot_image = sandbox.snapshot_filesystem()

                # Log and update the new snapshot ID
                snapshot_id = _extract_snapshot_id(new_snapshot_image)
                if snapshot_id:
                    logger.info(f"📸 New snapshot created with ID: {snapshot_id}")

                    # Update repository record with new snapshot ID
                    try:
                        await convex_client.update_repository_snapshot_id(
                            effective_repo_id, snapshot_id
                        )
                        logger.info(
                            f"✅ Updated repository {effective_repo_id} with snapshot ID: {snapshot_id}"
                        )
                    except Exception as e:
                        logger.warning(
                            f"⚠️ Failed to update repository snapshot ID: {e}"
                        )

                    logger.info(f"✅ New snapshot created with ID: {snapshot_id}")
                else:
                    logger.warning("⚠️ Could not retrieve new snapshot ID")

            except Exception as e:
                logger.warning(
                    f"⚠️ Failed to create new snapshot after manual setup: {e}"
                )

        # Verify Claude Code is accessible
        await _verify_claude_setup(sandbox)

        logger.info("🎉 Modal sandbox setup complete!")

        # Complete tracing with success
        if run_tree:
            try:
                run_tree.outputs = {
                    "status": "success",
                    "message": "Modal sandbox created successfully",
                    "sandbox_id": str(sandbox.object_id) if hasattr(sandbox, 'object_id') else "unknown"
                }
                run_tree.end()
                run_tree.patch()
                logger.info(f"✅ Completed create_sandbox trace")
            except Exception as e:
                logger.warning(f"⚠️ Failed to complete create_sandbox tracing: {e}")

        return sandbox

    except Exception as e:
        logger.error(f"💥 Failed to create Modal sandbox: {e}")

        # Complete tracing with error
        if run_tree:
            try:
                run_tree.outputs = {
                    "status": "error",
                    "error": str(e)
                }
                run_tree.end()
                run_tree.patch()
                logger.info(f"❌ Completed create_sandbox trace with error")
            except Exception as trace_e:
                logger.warning(f"⚠️ Failed to complete create_sandbox error tracing: {trace_e}")

        raise


async def _setup_repository_in_sandbox(
    sandbox: modal.Sandbox,
    repo_id: str,  # noqa: ARG001
    github_token: str,
    repo_url: str,
) -> None:
    """Set up repository in sandbox when not using snapshots."""
    logger.info("📂 Setting up repository in sandbox...")

    try:
        repo_path = repo_url.replace("https://github.com/", "")

        # Clone the repository
        clone_cmd = (
            f"git clone https://x-access-token:{github_token}@github.com/{repo_path} ."
        )
        clone_process = sandbox.exec("bash", "-c", clone_cmd)
        clone_output = clone_process.stdout.read()
        clone_process.wait()  # Wait for process to complete

        if clone_process.returncode != 0:
            error_msg = clone_process.stderr.read()
            raise Exception(f"Failed to clone repository: {error_msg}")

        logger.info("✅ Repository cloned successfully")

        # Configure git
        git_config_cmd = "git config user.email '<EMAIL>' && git config user.name 'Backspace Agent'"
        config_process = sandbox.exec("bash", "-c", git_config_cmd)
        config_process.stdout.read()

        # Setup dependencies
        await _setup_project_dependencies(sandbox)

        # Verify setup
        ls_process = sandbox.exec("bash", "-c", "ls -la")
        ls_output = ls_process.stdout.read()
        logger.info(f"📋 Workspace contents:\n{ls_output}")

    except Exception as e:
        logger.error(f"⚠️ Failed to setup repository: {e}")
        raise


async def _verify_claude_setup(sandbox: modal.Sandbox) -> None:
    """Verify Claude Code is accessible and MCP servers are configured."""
    from utils.mcp import verify_claude_code_with_mcp
    
    if not verify_claude_code_with_mcp(sandbox):
        logger.error("❌ Claude Code or MCP setup verification failed")
        raise Exception("Claude Code or MCP is not properly configured in the sandbox")
    
    logger.info("🎉 Claude Code and MCP integration ready!")


async def cleanup_sandbox(sandbox: Optional[modal.Sandbox]) -> None:
    """Clean up the Modal sandbox.

    Args:
        sandbox: The sandbox instance to clean up

    Raises:
        Exception: If cleanup fails
    """
    if sandbox:
        logger.info(f"🧹 Cleaning up Modal sandbox...")

        try:
            sandbox.terminate()
            logger.info("✅ Successfully cleaned up Modal sandbox")
        except Exception as e:
            logger.error(f"⚠️ Error during sandbox cleanup: {e}")
            raise


@asynccontextmanager
async def sandbox_context(
    repo_id: Optional[str] = None,
    envs: Optional[Dict[str, str]] = None,
    timeout: int = 300,
    use_snapshot: bool = True,
):
    """Context manager for Modal sandbox lifecycle management.

    Args:
        repo_id: Repository ID to get GitHub installation_id from database. If None, uses env DEFAULT_REPO_ID.
        envs: Additional environment variables to set
        timeout: Sandbox creation timeout in seconds (default: 300s/5min)
        use_snapshot: Whether to use snapshot for faster setup (default: True)

    Yields:
        sandbox: The sandbox instance

    Example:
        async with sandbox_context(repo_id="12345", timeout=1000) as sandbox:
            # Use sandbox here with 1000s timeout
            process = sandbox.exec("ls", "-la")
            result = process.stdout.read()
        # Sandbox automatically cleaned up
    """
    sandbox = None

    try:
        logger.info("🌟 Entering Modal sandbox context...")
        sandbox = await create_sandbox(
            repo_id=repo_id,
            envs=envs,
            timeout=timeout,
            use_snapshot=use_snapshot,
        )
        yield sandbox
    except Exception as e:
        logger.error(f"💥 Error in sandbox context: {e}")
        raise
    finally:
        if sandbox:
            logger.info("🌙 Exiting sandbox context...")
            await cleanup_sandbox(sandbox)


async def run_command_in_sandbox(
    sandbox: modal.Sandbox, command: str, cwd: Optional[str] = None, timeout: int = 120
) -> Dict[str, Any]:
    """Run a command in the Modal sandbox with detailed logging.

    Args:
        sandbox: The Modal sandbox instance
        command: Command to run
        cwd: Working directory (optional)
        timeout: Command timeout in seconds

    Returns:
        Dict with exit_code, stdout, stderr, and duration
    """
    logger.info(
        f"⚡ Running command: {command[:100]}{'...' if len(command) > 100 else ''}"
    )
    if cwd:
        logger.debug(f"   📁 Working directory: {cwd}")
        command = f"cd {cwd} && {command}"

    import time

    start_time = time.time()

    try:
        process = sandbox.exec("bash", "-c", command, timeout=timeout)
        stdout = process.stdout.read()
        stderr = process.stderr.read()
        process.wait()  # Wait for process to complete
        exit_code = process.returncode

        duration = time.time() - start_time

        if exit_code == 0:
            logger.info(f"   ✅ Command succeeded in {duration:.2f}s")
        else:
            logger.warning(
                f"   ⚠️ Command failed with exit code {exit_code} in {duration:.2f}s"
            )

        if stdout:
            logger.debug(
                f"   📤 STDOUT: {stdout[:200]}{'...' if len(stdout) > 200 else ''}"
            )
        if stderr:
            logger.debug(
                f"   📥 STDERR: {stderr[:200]}{'...' if len(stderr) > 200 else ''}"
            )

        return {
            "exit_code": exit_code,
            "stdout": stdout,
            "stderr": stderr,
            "duration": duration,
        }

    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"   💥 Command failed after {duration:.2f}s: {e}")
        return {
            "exit_code": 1,
            "stdout": "",
            "stderr": f"Command execution failed: {e}",
            "duration": duration,
        }


async def run_claude(
    sandbox: modal.Sandbox,
    prompt: str,
    claude_options: Optional[Dict[str, Any]] = None,
    on_output: Optional[Callable[[ClaudeOutput], None]] = None,
    cwd: Optional[str] = None,
    timeout: int = 300,
    stream: bool = False,
    max_retries: int = 2,
    enable_tracing: bool = True,
    parent_run: Optional[Any] = None,
) -> ClaudeSession:
    """Run Claude Code in the Modal sandbox with the given prompt and retry on failures.

    Args:
        sandbox: The Modal sandbox instance
        prompt: The prompt to send to Claude
        claude_options: Additional options for Claude CLI (e.g., --model, --max-turns)
        on_output: Optional callback for each output
        cwd: Working directory for Claude
        timeout: Command timeout in seconds
        stream: Whether to stream output in real-time
        max_retries: Maximum number of retry attempts (default: 2)
        enable_tracing: Whether to enable LangSmith tracing (default: True)
        parent_run: Optional parent run for nested tracing

    Returns:
        ClaudeSession object containing all outputs and metadata
    """
    session_id = f"modal-{id(sandbox)}-{int(datetime.now().timestamp())}"
    session = ClaudeSession(session_id=session_id, prompt=prompt)

    # Initialize tracing if enabled
    if enable_tracing:
        session.trace_enabled = True
        session.init_tracing(parent_run=parent_run)

    logger.info(f"🤖 Starting Claude Code session: {session_id}")
    logger.info(f"📝 Prompt: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")

    # Track retry attempts
    attempt = 0
    last_error = None

    while attempt <= max_retries:
        try:
            if attempt == 0:
                # First attempt - use original prompt
                result = await _execute_claude_attempt(
                    sandbox,
                    prompt,
                    claude_options,
                    on_output,
                    cwd,
                    timeout,
                    stream,
                    session,
                )
            else:
                # Retry attempt - use continue command
                continue_prompt = "Your last command ended in a crash. Continue where you left off and complete the task."
                logger.info(
                    f"🔄 Retry attempt {attempt}/{max_retries} - Using continue command"
                )
                result = await _execute_claude_continue(
                    sandbox,
                    continue_prompt,
                    claude_options,
                    on_output,
                    cwd,
                    timeout,
                    stream,
                    session,
                )

            # If we get here, the attempt succeeded
            if not session.end_time:  # If not already finalized
                session.finalize(success=True)

            logger.info(
                f"✅ Claude Code session completed successfully after {attempt + 1} attempt(s)"
            )
            return session

        except Exception as e:
            last_error = e
            attempt += 1

            error_msg = str(e)
            logger.warning(f"⚠️ Claude execution attempt {attempt} failed: {error_msg}")

            if attempt <= max_retries:
                logger.info(
                    f"🔄 Retrying with continue command ({attempt}/{max_retries})..."
                )
                # Add error info to session outputs for debugging
                error_output = ClaudeOutput(
                    timestamp=datetime.now().timestamp(),
                    type="retry_error",
                    content=f"Attempt {attempt} failed: {error_msg}",
                    raw_event=None,
                )
                session.add_output(error_output)
            else:
                logger.error(f"💥 All retry attempts failed. Final error: {error_msg}")
                break

    # All attempts failed
    final_error_msg = (
        f"Failed after {max_retries + 1} attempts. Last error: {last_error}"
    )
    logger.error(f"💥 {final_error_msg}")
    session.finalize(success=False, error=final_error_msg)

    logger.info(f"📊 Session summary:")
    logger.info(f"   ⏱️ Duration: {session.elapsed_time:.2f}s")
    logger.info(f"   📝 Outputs collected: {len(session.outputs)}")
    logger.info(f"   🔄 Retry attempts: {attempt}")
    if session.total_cost_usd:
        logger.info(f"   💰 Total cost: ${session.total_cost_usd:.4f}")

    return session


async def _execute_claude_attempt(
    sandbox: modal.Sandbox,
    prompt: str,
    claude_options: Optional[Dict[str, Any]],
    on_output: Optional[Callable[[ClaudeOutput], None]],
    cwd: Optional[str],
    timeout: int,
    stream: bool,
    session: ClaudeSession,
) -> None:
    """Execute a Claude command attempt with the original prompt."""
    # Build Claude command
    cmd_parts = [
        "claude",
        "-p",
        "--output-format",
        "stream-json",
        "--verbose",
        "--allowedTools",
        "Edit,Write,MultiEdit,Read,Bash",
    ]

    # Add any additional options
    if claude_options:
        # Skip options that are for Modal sandbox, not Claude
        skip_options = {"use_snapshot"}
        for key, value in claude_options.items():
            if key in skip_options:
                continue
            if key.startswith("--"):
                cmd_parts.append(key)
                if value is not None:
                    cmd_parts.append(str(value))
            else:
                cmd_parts.append(f"--{key}")
                if value is not None:
                    cmd_parts.append(str(value))

    # Add the prompt via echo
    escaped_prompt = prompt.replace("'", "'\"'\"'")
    claude_command = " ".join(cmd_parts)
    full_command = f"echo '{escaped_prompt}' | {claude_command}"

    if cwd:
        full_command = f"cd {cwd} && {full_command}"

    logger.debug(f"🔧 Full command: {full_command[:200]}...")
    logger.info("⚡ Executing Claude Code...")

    await _run_claude_command(
        sandbox, full_command, timeout, stream, session, on_output
    )


async def _execute_claude_continue(
    sandbox: modal.Sandbox,
    continue_prompt: str,
    claude_options: Optional[Dict[str, Any]],
    on_output: Optional[Callable[[ClaudeOutput], None]],
    cwd: Optional[str],
    timeout: int,
    stream: bool,
    session: ClaudeSession,
) -> None:
    """Execute a Claude continue command to recover from crashes."""
    # Build Claude continue command
    cmd_parts = [
        "claude",
        "--continue",
        "-p",
        "--output-format",
        "stream-json",
        "--verbose",
    ]

    # Add any additional options (excluding those not compatible with --continue)
    if claude_options:
        skip_options = {
            "use_snapshot",
            "allowedTools",
        }  # --continue may not support all options
        for key, value in claude_options.items():
            if key in skip_options:
                continue
            if key.startswith("--"):
                cmd_parts.append(key)
                if value is not None:
                    cmd_parts.append(str(value))
            else:
                cmd_parts.append(f"--{key}")
                if value is not None:
                    cmd_parts.append(str(value))

    # Add the continue prompt via echo
    escaped_prompt = continue_prompt.replace("'", "'\"'\"'")
    claude_command = " ".join(cmd_parts)
    full_command = f"echo '{escaped_prompt}' | {claude_command}"

    if cwd:
        full_command = f"cd {cwd} && {full_command}"

    logger.debug(f"🔧 Continue command: {full_command[:200]}...")
    logger.info("🔄 Executing Claude Code --continue...")

    await _run_claude_command(
        sandbox, full_command, timeout, stream, session, on_output
    )


async def _run_claude_command(
    sandbox: modal.Sandbox,
    full_command: str,
    timeout: int,
    stream: bool,
    session: ClaudeSession,
    on_output: Optional[Callable[[ClaudeOutput], None]],
) -> None:
    """Execute the actual Claude command and handle output."""
    process = sandbox.exec("bash", "-c", full_command, timeout=timeout)

    try:
        if stream:
            # Stream output line by line as Modal provides it
            logger.info("🌊 Streaming output...")
            try:
                for line in process.stdout:
                    # Process each line as it arrives
                    try:
                        # Handle potential UTF-8 decode issues
                        if isinstance(line, bytes):
                            line = line.decode('utf-8', errors='replace')
                        line = line.rstrip("\n")
                        if line:
                            try:
                                output = _log_stream(line, session)
                                if output and on_output:
                                    if asyncio.iscoroutinefunction(on_output):
                                        await on_output(output)
                                    else:
                                        on_output(output)
                            except UnicodeDecodeError as e:
                                logger.warning(f"⚠️ Unicode decode error in stream line: {e}")
                                # Continue processing other lines
                                continue
                            except Exception as e:
                                logger.warning(f"⚠️ Error processing stream line: {e}")
                                continue
                    except UnicodeDecodeError as e:
                        logger.warning(f"⚠️ Unicode decode error reading line: {e}")
                        # Skip this line and continue
                        continue
            except UnicodeDecodeError as e:
                logger.error(f"⚠️ Critical Unicode decode error in stdout stream: {e}")
                # Fall back to non-streaming mode
                logger.info("🔄 Falling back to non-streaming mode due to encoding issues")
                stream = False

            # Also check stderr
            stderr_output = process.stderr.read()
            if stderr_output:
                try:
                    for line in stderr_output.split("\n"):
                        if line.strip():
                            logger.warning(f"⚠️ STDERR: {line}")
                except UnicodeDecodeError as e:
                    logger.warning(f"⚠️ Unicode decode error in stderr: {e}")
        else:
            # Read all at once for non-streaming mode
            try:
                stdout = process.stdout.read()
                stderr = process.stderr.read()
            except UnicodeDecodeError as e:
                logger.error(f"💥 Unicode decode error reading output: {e}")
                raise Exception(f"Unicode decode error: {e}")

            process.wait()  # Wait for process to complete before checking returncode

            if stdout:
                stdout_lines = stdout.split("\n")
                # Process each line from stdout
                for line in stdout_lines:
                    if line.strip():
                        try:
                            output = _log_stream(line, session)
                            if output and on_output:
                                if asyncio.iscoroutinefunction(on_output):
                                    await on_output(output)
                                else:
                                    on_output(output)
                        except Exception as e:
                            logger.warning(f"⚠️ Error processing stdout line: {e}")
                            continue

            # Log stderr if present
            if stderr:
                try:
                    stderr_lines = stderr.split("\n")
                    for line in stderr_lines:
                        if line.strip():
                            logger.warning(f"⚠️ STDERR: {line}")
                except UnicodeDecodeError as e:
                    logger.warning(f"⚠️ Unicode decode error in stderr: {e}")

        # Check return code only if session wasn't already finalized with success
        # Claude's "result" event is the authoritative source of success/failure
        if session and session.success:
            logger.debug(
                "✅ Session already finalized with success, skipping return code check"
            )
        else:
            # Need to wait for process completion to get return code
            if stream:
                process.wait()

            if process.returncode != 0:
                error_msg = f"Claude command exited with code {process.returncode}"
                logger.error(f"❌ {error_msg}")
                stderr = process.stderr.read() if stream else stderr
                if stderr:
                    try:
                        error_msg += f": {stderr}"
                    except UnicodeDecodeError:
                        error_msg += ": <stderr contains non-UTF8 data>"
                raise Exception(error_msg)

    except UnicodeDecodeError as e:
        logger.error(f"💥 Unicode decode error during command execution: {e}")
        raise Exception(f"Unicode decode error: {e}")
    except Exception as e:
        logger.error(f"💥 Command execution error: {e}")
        raise



def _log_stream(
    line: str, session: Optional[ClaudeSession] = None
) -> Optional[ClaudeOutput]:
    """Handle a single line from Claude's stream output.

    Args:
        line: Raw line from Claude's JSON stream
        session: Optional session to store outputs in

    Returns:
        ClaudeOutput object if successfully parsed, None otherwise
    """
    if not line.strip():
        return None

    try:
        # Ensure line is properly encoded string
        if isinstance(line, bytes):
            line = line.decode('utf-8', errors='replace')
        event = json.loads(line)
        event_type = event.get("type")
        timestamp = datetime.now().timestamp()
        output = None

        if event_type == "system":
            logger.info("🚀 SYSTEM INIT")
            logger.info(f"   📁 CWD: {event.get('cwd')}")
            logger.info(f"   🤖 Model: {event.get('model')}")
            logger.info(f"   🛡️ Permission Mode: {event.get('permissionMode')}")

            output = ClaudeOutput(
                timestamp=timestamp,
                type="system",
                content={
                    "cwd": event.get("cwd"),
                    "model": event.get("model"),
                    "permissionMode": event.get("permissionMode"),
                },
                raw_event=event,
            )

        elif event_type == "assistant":
            msg = event.get("message", {})
            content = msg.get("content", [])

            for item in content:
                # Handle both string and dict formats
                if isinstance(item, str):
                    logger.info(f"🤖 Claude: {item}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="claude_message",
                        content=item,
                        raw_event=event,
                    )
                elif isinstance(item, dict) and item.get("type") == "text":
                    text = item.get("text", "")
                    logger.info(f"🤖 Claude: {text}")

                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="claude_message",
                        content=text,
                        raw_event=event,
                    )

                elif isinstance(item, dict) and item.get("type") == "tool_use":
                    tool = item.get("name")
                    tool_id = item.get("id")  # Extract the tool_use_id
                    inp = item.get("input", {})

                    tool_emojis = {
                        "Write": "📝",
                        "Bash": "⚡",
                        "Read": "👀",
                        "Edit": "✏️",
                        "MultiEdit": "✂️",
                        "TodoRead": "📋",
                        "TodoWrite": "✅",
                        "WebSearch": "🔍",
                        "WebFetch": "🌐",
                    }

                    emoji = tool_emojis.get(tool, "🔧")

                    if tool == "Write":
                        logger.info(f"{emoji} Writing: {inp.get('file_path')}")
                    elif tool == "Bash":
                        logger.info(f"{emoji} Running: {inp.get('command')}")
                    elif tool == "Read":
                        logger.info(f"{emoji} Reading: {inp.get('file_path')}")
                    elif tool == "Edit":
                        logger.info(f"{emoji} Editing: {inp.get('file_path')}")
                    else:
                        logger.info(f"{emoji} Tool: {tool}")

                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_call",
                        content={"name": tool, "input": inp, "id": tool_id},  # Include the tool_use_id
                        raw_event=event,
                    )

        elif event_type == "user":
            msg = event.get("message", {})
            content = msg.get("content", [])

            for item in content:
                # Handle both string and dict formats
                if isinstance(item, str):
                    logger.info(f"👤 User: {item}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="user_message",
                        content=item,
                        raw_event=event,
                    )
                elif isinstance(item, dict) and item.get("type") == "text":
                    text = item.get("text", "")
                    logger.info(f"👤 User: {text}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="user_message",
                        content=text,
                        raw_event=event,
                    )
                elif isinstance(item, dict) and item.get("type") == "tool_result":
                    # REAL tool results are embedded in user messages!
                    tool_use_id = item.get("tool_use_id")
                    tool_content = item.get("content", "")

                    logger.info(f"🔧 REAL Tool Result (ID: {tool_use_id}): {str(tool_content)[:200]}{'...' if len(str(tool_content)) > 200 else ''}")

                    # Create tool_result output with the REAL tool content
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_result",
                        content={
                            "tool_use_id": tool_use_id,
                            "content": tool_content,  
                            "status": "✅",
                            "length": len(str(tool_content))
                        },
                        raw_event=event,
                    )

                    # Update pending tool with REAL result
                    if session and hasattr(session, 'pending_tools') and tool_use_id in session.pending_tools:
                        tool_info = session.pending_tools.pop(tool_use_id)

                        # Handle new dict format
                        tool_run = tool_info["run"]
                        start_timestamp = tool_info["start_timestamp"]
                        actual_duration_ms = int((timestamp - start_timestamp) * 1000)

                        tool_run.name += " ✅"
                        tool_run.end(
                            outputs={
                                "result": tool_content,
                                "status": "✅",
                                "tool_use_id": tool_use_id,
                                "length": len(str(tool_content)),
                                "type": "real_content",
                                "actual_duration_ms": actual_duration_ms
                            },
                            end_time=datetime.fromtimestamp(timestamp, tz=timezone.utc)
                        )
                        tool_run.patch()

        elif event_type == "tool_result":
            tool_use_id = event.get("tool_use_id")
            content = event.get("content", [])

            for item in content:
                # Handle both string and dict formats
                if isinstance(item, str):
                    logger.info(
                        f"🔧 Tool Result: {item[:100]}{'...' if len(item) > 100 else ''}"
                    )
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_result",
                        content={"tool_use_id": tool_use_id, "result": item},
                        raw_event=event,
                    )
                elif isinstance(item, dict) and item.get("type") == "text":
                    text = item.get("text", "")
                    logger.info(
                        f"🔧 Tool Result: {text[:100]}{'...' if len(text) > 100 else ''}"
                    )
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_result",
                        content={"tool_use_id": tool_use_id, "result": text},
                        raw_event=event,
                    )

        elif event_type == "result":
            result = event.get("result", {})

            # Handle both string and dict formats for result
            if isinstance(result, str):
                # When result is a string, extract metadata from the event itself
                total_cost_usd = event.get("total_cost_usd")
                duration_ms = event.get("duration_ms")
                is_error = event.get("is_error", False)
                result_content = result
            else:
                # When result is a dict, use the original logic
                total_cost_usd = result.get("total_cost_usd")
                duration_ms = result.get("duration_ms")
                is_error = result.get("error") is not None
                result_content = result.get("error") or result.get("output", "Success")

            logger.info(f"📊 RESULT:")
            if total_cost_usd:
                logger.info(f"   💰 Cost: ${total_cost_usd:.4f}")
            if duration_ms:
                logger.info(f"   ⏱️ Duration: {duration_ms}ms")

            if is_error:
                logger.error(f"   ❌ Error: {result_content}")
            else:
                logger.info(f"   ✅ Success")

            output = ClaudeOutput(
                timestamp=timestamp, type="result", content=result, raw_event=event
            )

            # Update session if provided
            if session:
                session.duration_ms = duration_ms
                session.total_cost_usd = total_cost_usd
                session.finalize(
                    success=not is_error, error=result_content if is_error else None
                )

        elif event_type == "error":
            error_msg = event.get("error", event.get("message", "Unknown error"))
            logger.error(f"💥 Error: {error_msg}")

            output = ClaudeOutput(
                timestamp=timestamp, type="error", content=error_msg, raw_event=event
            )

            if session:
                session.finalize(success=False, error=error_msg)

        # Add output to session if provided
        if output and session:
            session.add_output(output)

        return output

    except json.JSONDecodeError as e:
        logger.debug(f"⚠️ Failed to parse JSON line: {line[:50]}... - {e}")
        return None
    except UnicodeDecodeError as e:
        logger.warning(f"⚠️ Unicode decode error in JSON parsing: {e}")
        return None
    except Exception as e:
        logger.error(f"💥 Unexpected error handling stream: {e}")
        logger.error(
            f"💥 Event type: {event.get('type') if 'event' in locals() else 'unknown'}"
        )
        logger.error(f"💥 Line content: {line[:200]}...")
        import traceback

        logger.error(f"💥 Traceback: {traceback.format_exc()}")
        return None


# ─────────────────────────────── Convenience Functions ─────────────────────────────── #

async def run_claude_with_tracing(
    sandbox: modal.Sandbox,
    prompt: str,
    claude_options: Optional[Dict[str, Any]] = None,
    cwd: Optional[str] = None,
    timeout: int = 300,
    enable_tracing: bool = True,
    parent_run: Optional[Any] = None,
) -> ClaudeSession:
    """Convenience wrapper for run_claude with explicit tracing control.

    Args:
        sandbox: The Modal sandbox instance
        prompt: The prompt to send to Claude
        claude_options: Additional options for Claude CLI
        cwd: Working directory for Claude
        timeout: Command timeout in seconds
        enable_tracing: Whether to enable LangSmith tracing
        parent_run: Optional parent run for nested tracing

    Returns:
        ClaudeSession object with tracing enabled
    """
    return await run_claude(
        sandbox=sandbox,
        prompt=prompt,
        claude_options=claude_options,
        cwd=cwd,
        timeout=timeout,
        stream=True,  # Always use streaming for better tracing
        enable_tracing=enable_tracing,
        parent_run=parent_run,
    )


def check_tracing_config() -> Dict[str, Any]:
    """Check the current tracing configuration.

    Returns:
        Dict with tracing configuration status
    """
    config = {
        "langsmith_available": LANGSMITH_AVAILABLE,
        "tracing_enabled": False,
        "api_key_set": False,
        "project": None,
        "endpoint": None,
    }

    # Check for both LANGSMITH_* and LANGCHAIN_* variables
    api_key = os.getenv("LANGSMITH_API_KEY") or os.getenv("LANGCHAIN_API_KEY")
    tracing_enabled = (
        os.getenv("LANGSMITH_TRACING", "false").lower() == "true" or
        os.getenv("LANGCHAIN_TRACING_V2", "false").lower() == "true"
    )
    project = (
        os.getenv("LANGSMITH_PROJECT") or
        os.getenv("LANGCHAIN_PROJECT")
    )
    endpoint = (
        os.getenv("LANGSMITH_ENDPOINT") or
        os.getenv("LANGCHAIN_ENDPOINT") or
        "https://api.smith.langchain.com"
    )

    config.update({
        "tracing_enabled": tracing_enabled,
        "api_key_set": bool(api_key),
        "project": project,
        "endpoint": endpoint,
    })

    return config
