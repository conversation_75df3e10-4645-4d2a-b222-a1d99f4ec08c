#!/usr/bin/env python3
"""Test the timing fix for tool execution in LangSmith tracing."""

import asyncio
import os
import sys
from datetime import datetime
import time

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from sandbox.modal_sandbox import <PERSON><PERSON><PERSON><PERSON>, Claude<PERSON><PERSON><PERSON>


def test_timing_calculation():
    """Test that our timing calculation logic works correctly."""
    print("🧪 Testing Tool Timing Calculation Logic")
    print("=" * 50)

    try:
        # Create a mock session
        session = ClaudeSession(session_id="test-123", prompt="Test prompt")
        session.trace_enabled = True

        # Simulate tool call and result with realistic timing
        base_time = time.time()

        # Tool call happens first
        tool_call = ClaudeOutput(
            timestamp=base_time,
            type="tool_call",
            content={"name": "Bash", "input": {"command": "ls -la"}, "id": "tool_123"},
            raw_event={"type": "tool_use"}
        )

        # Tool result happens 2.5 seconds later (realistic bash execution time)
        tool_result = ClaudeOutput(
            timestamp=base_time + 2.5,  # 2500ms later
            type="tool_result",
            content={"tool_use_id": "tool_123", "result": "file1.txt\nfile2.py\n"},
            raw_event={"type": "tool_result"}
        )

        print(f"📊 Simulated timing:")
        print(f"   Tool call at: {tool_call.timestamp}")
        print(f"   Tool result at: {tool_result.timestamp}")
        print(f"   Expected duration: {int((tool_result.timestamp - tool_call.timestamp) * 1000)}ms")

        # Test our timing calculation logic
        start_timestamp = tool_call.timestamp
        end_timestamp = tool_result.timestamp
        calculated_duration = int((end_timestamp - start_timestamp) * 1000)

        print(f"\n🔧 Timing Calculation:")
        print(f"   Start: {start_timestamp}")
        print(f"   End: {end_timestamp}")
        print(f"   Duration: {calculated_duration}ms")

        # Verify it's reasonable
        if 2400 <= calculated_duration <= 2600:  # Allow some tolerance
            print(f"✅ Timing calculation looks correct!")
            return True
        else:
            print(f"❌ Timing calculation seems wrong: expected ~2500ms, got {calculated_duration}ms")
            return False

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # Test the timing calculation logic
    success = test_timing_calculation()
    print(f"\n{'✅ Timing calculation test passed!' if success else '❌ Timing calculation test failed!'}")

    print(f"\n🎯 The fix should now provide accurate tool timing in LangSmith!")
    print(f"   - Tool calls now store their actual timestamp")
    print(f"   - Tool results calculate duration from the timestamp difference")
    print(f"   - This should show realistic timing (seconds, not milliseconds)")
    print(f"   - Check your LangSmith dashboard after running real Claude Code sessions")
