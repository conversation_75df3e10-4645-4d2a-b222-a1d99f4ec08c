{"name": "codeviz", "version": "1.0.0", "description": "Generate architecture diagrams from multi-language GitHub repositories using AST analysis and LLM. Supports HTML/CSS, JavaScript/TypeScript (Node.js/Wasm), Python, Go, Rust, and Ruby.", "main": "main.js", "type": "module", "scripts": {"start": "node main.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ast", "architecture", "diagram", "mermaid", "github", "code-analysis", "multi-language", "html", "css", "javascript", "typescript", "python", "go", "ruby", "rust", "tree-sitter", "babel"], "author": "", "license": "MIT", "dependencies": {"@babel/parser": "^7.22.16", "@babel/traverse": "^7.22.15", "@babel/types": "^7.22.15", "chalk": "^5.3.0", "commander": "^11.0.0", "dotenv": "^17.0.0", "express": "^5.1.0", "fs-extra": "^11.1.1", "ignore": "^5.2.4", "mustache": "^4.2.0", "openai": "^4.52.7", "ora": "^7.0.1", "simple-git": "^3.19.1", "tree-sitter": "^0.25.0", "tree-sitter-go": "^0.23.4", "tree-sitter-python": "^0.23.6", "tree-sitter-ruby": "^0.23.1", "tree-sitter-rust": "^0.24.0"}}