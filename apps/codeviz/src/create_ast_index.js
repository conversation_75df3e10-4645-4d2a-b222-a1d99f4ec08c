#!/usr/bin/env node

import fs from "fs-extra";
import path from "path";
import {
  parseFile,
  getFilesToParse,
  getLanguageStats,
} from "./utils/multi-language-parser.js";

/**
 * Main function to create simplified code index
 */
export async function createCodeIndex(projectPath, options = {}) {
  const {
    outputPath = path.join(process.cwd(), "code_index.json"),
    verbose = false,
  } = options;

  console.log(`Analyzing files in: ${projectPath}`);

  try {
    // Get all files to parse
    const filePaths = await getFilesToParse(projectPath);
    console.log(`Found ${filePaths.length} files to parse`);

    if (filePaths.length === 0) {
      throw new Error("No supported files found in the project");
    }

    // Parse files
    const files = [];
    let processed = 0;

    for (const filePath of filePaths) {
      try {
        const content = await fs.readFile(filePath, "utf8");
        const relativePath = path.relative(projectPath, filePath);
        const fileData = parseFile(relativePath, content);

        // Only include files without errors and with actual data
        if (!fileData.error) {
          files.push({
            path: fileData.path,
            imports: fileData.imports,
            exports: fileData.exports,
            functions: fileData.functions || [],
            classes: fileData.classes || [],
          });
        }

        processed++;
        if (processed % 10 === 0 || processed === filePaths.length) {
          process.stdout.write(
            `\rProcessed ${processed}/${filePaths.length} files...`,
          );
        }

        if (verbose && fileData.error) {
          console.warn(`\nWarning: ${fileData.error}`);
        }
      } catch (error) {
        console.warn(`\nFailed to read file ${filePath}:`, error.message);
      }
    }

    console.log(`\nGenerated index for ${files.length} files...`);

    // Generate language statistics
    const languageStats = getLanguageStats(files);

    // Log language breakdown
    console.log("\nLanguage breakdown:");
    Object.entries(languageStats).forEach(([lang, stats]) => {
      console.log(
        `  ${lang}: ${stats.fileCount} files, ${stats.totalFunctions} functions, ${stats.totalClasses} classes`,
      );
    });

    const result = {
      metadata: {
        projectPath,
        createdAt: new Date().toISOString(),
        totalFiles: files.length,
        languageStats,
      },
      files,
    };

    // Save to file
    await fs.writeFile(outputPath, JSON.stringify(result, null, 2));
    console.log(`Code index created: ${outputPath}`);
    console.log(`Processed ${files.length} files successfully`);

    return result;
  } catch (error) {
    console.error("Failed to create code index:", error.message);
    throw error;
  }
}

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
  const projectPath = process.argv[2];

  if (!projectPath) {
    console.error("Usage: node create_ast_index.js <project-path>");
    process.exit(1);
  }

  try {
    await createCodeIndex(projectPath, { verbose: true });
  } catch (error) {
    console.error("Error:", error.message);
    process.exit(1);
  }
}
