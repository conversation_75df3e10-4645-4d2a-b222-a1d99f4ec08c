#!/usr/bin/env node

import fs from "fs-extra";
import path from "path";
import { createLLMClient } from "./utils/llm-client.js";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Load and validate the code index file
 * @param {string} indexPath - Path to the code index JSON file
 * @returns {object} Loaded and validated index data
 */
async function loadCodeIndex(indexPath) {
  try {
    const indexData = await fs.readJson(indexPath);

    // Validate required structure
    if (!indexData.metadata || !indexData.files) {
      throw new Error("Invalid index file: missing metadata or files data");
    }

    console.log(
      `Loaded index for project: ${
        indexData.metadata?.projectPath || "unknown"
      }`,
    );
    console.log(`Files analyzed: ${indexData.metadata?.totalFiles || 0}`);

    // Extract key dependencies for display
    const allDependencies = new Set();
    indexData.files.forEach((file) => {
      file.imports.forEach((imp) => {
        if (!imp.startsWith(".")) {
          allDependencies.add(imp);
        }
      });
    });

    const topDeps = Array.from(allDependencies).slice(0, 5);
    console.log(`Key dependencies: ${topDeps.join(", ") || "none detected"}`);

    return indexData;
  } catch (error) {
    if (error.code === "ENOENT") {
      throw new Error(
        `Code index file not found: ${indexPath}\nRun create_ast_index.js first to generate the index.`,
      );
    }
    throw new Error(`Failed to load code index: ${error.message}`);
  }
}

/**
 * Generate architecture relationships using LLM
 * @param {object} indexData - Loaded and enhanced index data
 * @param {object} options - Generation options
 * @returns {object} Generated architecture
 */
async function generateArchitectureRelationships(indexData, options = {}) {
  const { verbose = false } = options;

  try {
    console.log("Connecting to LLM service...");
    const llmClient = createLLMClient();

    console.log("Generating architecture relationships...");
    const architecture = await llmClient.generateArchitecture(indexData);

    console.log("✅ Architecture relationships generated successfully");

    // Validate the generated architecture
    const validation = validateArchitecture(architecture);
    if (!validation.isValid) {
      console.warn("⚠️  Generated architecture has validation warnings:");
      validation.warnings.forEach((warning) => console.warn(`   ${warning}`));
    }

    return architecture;
  } catch (error) {
    console.error(
      "Failed to generate architecture relationships:",
      error.message,
    );
    throw error;
  }
}

/**
 * Validate generated architecture
 * @param {object} architecture - Generated architecture
 * @returns {boolean} True if valid
 */
function validateArchitecture(architecture) {
  const issues = [];

  // Check basic structure
  if (!architecture.nodes || architecture.nodes.length === 0) {
    issues.push("No nodes generated");
  }

  if (!architecture.edges || architecture.edges.length === 0) {
    issues.push("No edges generated");
  }

  // Check for isolated nodes
  const connectedNodes = new Set();
  architecture.edges?.forEach((edge) => {
    connectedNodes.add(edge.source);
    connectedNodes.add(edge.target);
  });

  const isolatedNodes =
    architecture.nodes?.filter((node) => !connectedNodes.has(node.id)) || [];

  if (isolatedNodes.length > architecture.nodes.length * 0.3) {
    issues.push(
      `Too many isolated nodes: ${isolatedNodes.length}/${architecture.nodes.length}`,
    );
  }

  // Check for reasonable complexity
  if (architecture.nodes?.length > 20) {
    issues.push(
      "Architecture too complex (>20 nodes), may be difficult to read",
    );
  }

  if (issues.length > 0) {
    console.warn("Architecture validation issues:");
    issues.forEach((issue) => console.warn(`  - ${issue}`));
    return { isValid: false, warnings: issues };
  }

  return { isValid: true, warnings: [] };
}

/**
 * Main function to generate relationships
 * @param {string} indexPath - Path to code index file
 * @param {object} options - Generation options
 * @returns {object} Generated architecture
 */
export async function generateRelationships(
  indexPath = "code_index.json",
  options = {},
) {
  const {
    outputPath = "architecture.json",
    verbose = false,
    validate = true,
  } = options;

  try {
    // Load the code index
    const indexData = await loadCodeIndex(indexPath);

    // Generate architecture using LLM
    const architecture = await generateArchitectureRelationships(indexData, {
      verbose,
    });

    // Validate if requested
    if (validate && !validateArchitecture(architecture)) {
      console.warn(
        "Generated architecture has validation issues but will proceed",
      );
    }

    // Add metadata
    const result = {
      metadata: {
        generatedAt: new Date().toISOString(),
        sourceIndex: indexPath,
        provider: "openai",
        version: "1.0.0",
      },
      ...architecture,
    };

    // Save to file
    await fs.writeFile(outputPath, JSON.stringify(result, null, 2));
    console.log(`\nArchitecture relationships saved to: ${outputPath}`);

    return result;
  } catch (error) {
    console.error("Failed to generate relationships:", error.message);
    throw error;
  }
}

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
  const indexPath = process.argv[2] || "code_index.json";

  try {
    await generateRelationships(indexPath, {
      verbose: true,
      validate: true,
    });
  } catch (error) {
    console.error("Error:", error.message);
    process.exit(1);
  }
}
