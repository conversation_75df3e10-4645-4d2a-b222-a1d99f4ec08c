/**
 * Advanced Mermaid diagram builder
 * Generates sophisticated diagrams with subgraphs, styling, and proper formatting
 *
 * ASPECT RATIO CONTROL:
 * This builder includes intelligent aspect ratio control to prevent diagrams from becoming too wide.
 * Features:
 * - Automatic diagram direction selection (TD/LR) based on architecture analysis
 * - Smart subgraph organization to maintain compact layouts
 * - Configurable maximum width limits
 * - Force vertical layout option for consistently narrow diagrams
 *
 * Usage:
 * const builder = new MermaidBuilder({
 *   aspectRatioControl: {
 *     enabled: true,           // Enable aspect ratio optimization
 *     preferredRatio: 1.5,     // Preferred width:height ratio
 *     maxWidth: 10,            // Maximum nodes per row
 *     autoDirection: true,     // Auto-select TD/LR direction
 *     forceVertical: false,    // Force vertical (TD) layout
 *     compactSubgraphs: true   // Use compact subgraph organization
 *   }
 * });
 */

/**
 * Mermaid diagram builder class
 */
export class MermaidBuilder {
  constructor(options = {}) {
    this.diagramType = options.diagramType || "graph TD";
    this.theme = options.theme || "default";
    this.nodeStyles = options.nodeStyles || {};
    this.edgeStyles = options.edgeStyles || {};
    this.idCounter = options.startId || 5000;

    // Aspect ratio control options
    this.aspectRatioControl = {
      enabled: options.aspectRatioControl?.enabled ?? true,
      preferredRatio: options.aspectRatioControl?.preferredRatio || 1.5, // width:height ratio
      maxWidth: options.aspectRatioControl?.maxWidth || 12, // max nodes per row
      autoDirection: options.aspectRatioControl?.autoDirection ?? true,
      forceVertical: options.aspectRatioControl?.forceVertical || false,
      compactSubgraphs: options.aspectRatioControl?.compactSubgraphs ?? true,
    };

    this.content = [];
    this.nodeDefinitions = new Map();
    this.subgraphDefinitions = new Map();
    this.edgeDefinitions = [];
    this.styleDefinitions = [];
  }

  /**
   * Analyze architecture and determine optimal diagram direction
   * @param {object} architecture - Architecture data
   * @returns {string} Optimal diagram direction
   */
  analyzeOptimalDirection(architecture) {
    if (
      !this.aspectRatioControl.enabled ||
      !this.aspectRatioControl.autoDirection
    ) {
      return this.diagramType;
    }

    if (this.aspectRatioControl.forceVertical) {
      return "graph TD";
    }

    const nodes = architecture.nodes || [];
    const edges = architecture.edges || [];

    // Analyze architecture characteristics
    const nodeCount = nodes.length;
    const edgeCount = edges.length;

    // Calculate node type distribution
    const typeDistribution = {};
    nodes.forEach((node) => {
      const type = node.type || "Other";
      typeDistribution[type] = (typeDistribution[type] || 0) + 1;
    });

    // Analyze edge patterns to determine flow direction
    const horizontalFlow = this.analyzeFlowDirection(edges, nodes);

    // Decision logic for diagram direction
    if (nodeCount > this.aspectRatioControl.maxWidth) {
      // Too many nodes - prefer vertical layout
      return "graph TD";
    }

    if (horizontalFlow.score > 0.6) {
      // Strong horizontal flow - use left-to-right
      return "graph LR";
    }

    if (nodeCount <= 6 && edgeCount <= 8) {
      // Small diagram - top-down works well
      return "graph TD";
    }

    // Default to top-down for most cases
    return "graph TD";
  }

  /**
   * Analyze the predominant flow direction in the architecture
   * @param {Array} edges - Array of edges
   * @param {Array} nodes - Array of nodes
   * @returns {object} Flow analysis result
   */
  analyzeFlowDirection(edges, nodes) {
    const nodeTypes = {};
    nodes.forEach((node) => {
      nodeTypes[node.id] = node.type || "Other";
    });

    let horizontalFlowScore = 0;
    let verticalFlowScore = 0;

    edges.forEach((edge) => {
      const sourceType = nodeTypes[edge.source];
      const targetType = nodeTypes[edge.target];

      // Typical horizontal flows: Frontend -> Backend -> Database
      if (
        (sourceType === "Actor" && targetType === "Frontend") ||
        (sourceType === "Frontend" && targetType === "Backend") ||
        (sourceType === "Backend" && targetType === "External") ||
        (sourceType === "Backend" && targetType === "Database")
      ) {
        horizontalFlowScore += 1;
      }

      // Typical vertical flows: Service compositions, layered architectures
      if (
        (sourceType === "Service" && targetType === "Service") ||
        (sourceType === "Component" && targetType === "Component")
      ) {
        verticalFlowScore += 1;
      }
    });

    const totalEdges = edges.length || 1;
    return {
      horizontal: horizontalFlowScore,
      vertical: verticalFlowScore,
      score: horizontalFlowScore / totalEdges,
      recommendation: horizontalFlowScore > verticalFlowScore ? "LR" : "TD",
    };
  }

  /**
   * Generate compact subgraphs optimized for aspect ratio
   * @param {Array} nodes - Array of nodes
   * @returns {MermaidBuilder} For method chaining
   */
  generateCompactSubgraphs(nodes) {
    if (
      !this.aspectRatioControl.enabled ||
      !this.aspectRatioControl.compactSubgraphs
    ) {
      return this.autoGenerateSubgraphs(nodes);
    }

    const subgraphGroups = new Map();
    const standaloneNodes = [];

    // Group nodes with size awareness
    nodes.forEach((node) => {
      if (node.type === "Actor") {
        standaloneNodes.push(node.id);
        return;
      }

      const groupKey = this.determineCompactArchitecturalGroup(node);
      if (!subgraphGroups.has(groupKey)) {
        subgraphGroups.set(groupKey, []);
      }
      subgraphGroups.get(groupKey).push(node.id);
    });

    // Split large groups to maintain aspect ratio
    const maxNodesPerSubgraph = Math.max(
      3,
      Math.floor(this.aspectRatioControl.maxWidth / 2),
    );

    subgraphGroups.forEach((nodeIds, groupName) => {
      if (nodeIds.length <= maxNodesPerSubgraph) {
        // Small group - keep as is
        this.addSubgraph({
          id: groupName.toLowerCase().replace(/\s+/g, "_"),
          label: this.formatCompactSubgraphLabel(groupName, nodeIds.length),
          nodes: nodeIds,
        });
      } else {
        // Large group - split into smaller subgraphs
        const chunks = this.chunkArray(nodeIds, maxNodesPerSubgraph);
        chunks.forEach((chunk, index) => {
          this.addSubgraph({
            id: `${groupName.toLowerCase().replace(/\s+/g, "_")}_${index + 1}`,
            label: this.formatCompactSubgraphLabel(
              `${groupName} ${index + 1}`,
              chunk.length,
            ),
            nodes: chunk,
          });
        });
      }
    });

    return this;
  }

  /**
   * Determine compact architectural grouping for better aspect ratios
   * @param {object} node - Node to categorize
   * @returns {string} Group name
   */
  determineCompactArchitecturalGroup(node) {
    const type = node.type;
    const label = (node.label || "").toLowerCase();

    // More granular grouping for better aspect ratios
    if (type === "External" || type === "Database") {
      return "External Services";
    }

    if (
      type === "Frontend" ||
      label.includes("ui") ||
      label.includes("frontend")
    ) {
      return "User Interface";
    }

    if (type === "Backend" || type === "API" || label.includes("api")) {
      return "API Services";
    }

    if (type === "Service" || label.includes("service")) {
      return "Business Logic";
    }

    return "Core Components";
  }

  /**
   * Format compact subgraph labels
   * @param {string} groupName - Raw group name
   * @param {number} nodeCount - Number of nodes in group
   * @returns {string} Formatted label
   */
  formatCompactSubgraphLabel(groupName, nodeCount) {
    const cleanName = groupName
      .replace(/_/g, " ")
      .replace(/\b\w/g, (l) => l.toUpperCase());
    return `${cleanName}<br/>${nodeCount} Component${nodeCount !== 1 ? "s" : ""}`;
  }

  /**
   * Split array into chunks of specified size
   * @param {Array} array - Array to split
   * @param {number} chunkSize - Size of each chunk
   * @returns {Array} Array of chunks
   */
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Generate a unique ID for nodes
   * @returns {string} Unique ID
   */
  generateId() {
    return (++this.idCounter).toString();
  }

  /**
   * Add diagram header
   * @returns {MermaidBuilder} For method chaining
   */
  addHeader() {
    this.content.push(this.diagramType);
    this.content.push("");
    return this;
  }

  /**
   * Add a node to the diagram
   * @param {object} node - Node configuration
   * @returns {string} Generated node ID
   */
  addNode(node) {
    const id = node.id || this.generateId();
    const label = this.formatNodeLabel(node.label || node.name || "Unnamed");
    const shape = this.getNodeShape(node.type);

    const nodeDefinition = `${id}${shape.open}"${label}"${shape.close}`;
    this.nodeDefinitions.set(id, {
      definition: nodeDefinition,
      type: node.type,
      subgraph: node.subgraph,
      originalNode: node,
    });

    return id;
  }

  /**
   * Format node label with proper escaping and line breaks
   * @param {string} label - Raw label text
   * @returns {string} Formatted label
   */
  formatNodeLabel(label) {
    // Convert <br> tags to line breaks for Mermaid
    return label
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/&lt;br&gt;/g, "<br>");
  }

  /**
   * Get node shape based on type
   * @param {string} type - Node type
   * @returns {object} Shape configuration
   */
  getNodeShape(type) {
    const shapes = {
      Frontend: { open: "[", close: "]" },
      Backend: { open: "[", close: "]" },
      Service: { open: "[", close: "]" },
      DataAccess: { open: "[", close: "]" },
      External: { open: "[(", close: ")]" },
      Actor: { open: "(", close: ")" },
      Database: { open: "[(", close: ")]" },
      API: { open: "[", close: "]" },
      Component: { open: "[", close: "]" },
    };

    return shapes[type] || shapes["Component"];
  }

  /**
   * Add an edge between nodes
   * @param {object} edge - Edge configuration
   * @returns {MermaidBuilder} For method chaining
   */
  addEdge(edge) {
    const source = edge.source;
    const target = edge.target;
    const label = edge.label || "";

    let edgeDefinition;
    if (label) {
      edgeDefinition = `    ${source} -->|${label}| ${target}`;
    } else {
      edgeDefinition = `    ${source} --> ${target}`;
    }

    this.edgeDefinitions.push({
      definition: edgeDefinition,
      source,
      target,
      label,
      originalEdge: edge,
    });

    return this;
  }

  /**
   * Create a subgraph
   * @param {object} subgraph - Subgraph configuration
   * @returns {MermaidBuilder} For method chaining
   */
  addSubgraph(subgraph) {
    const id = subgraph.id || this.generateId();
    const label = subgraph.label || "Unnamed Group";
    const nodes = subgraph.nodes || [];

    this.subgraphDefinitions.set(id, {
      id,
      label,
      nodes,
      originalSubgraph: subgraph,
    });

    return this;
  }

  /**
   * Generate subgraphs automatically based on architectural layers and patterns
   * @param {Array} nodes - Array of nodes
   * @returns {MermaidBuilder} For method chaining
   */
  autoGenerateSubgraphs(nodes) {
    const subgraphGroups = new Map();
    const standaloneActors = [];

    // First, separate actors and external systems
    nodes.forEach((node) => {
      if (node.type === "Actor") {
        standaloneActors.push(node.id);
        return;
      }

      // Smart grouping based on architecture patterns
      const groupKey = this.determineArchitecturalGroup(node);
      if (!subgraphGroups.has(groupKey)) {
        subgraphGroups.set(groupKey, []);
      }
      subgraphGroups.get(groupKey).push(node.id);
    });

    // Create subgraphs for logical groups
    subgraphGroups.forEach((nodeIds, groupName) => {
      if (nodeIds.length > 0) {
        this.addSubgraph({
          id: groupName.toLowerCase().replace(/\s+/g, "_"),
          label: this.formatAdvancedSubgraphLabel(groupName, nodeIds.length),
          nodes: nodeIds,
        });
      }
    });

    return this;
  }

  /**
   * Determine the architectural group for a node based on type
   * @param {object} node - Node to categorize
   * @returns {string} Group name
   */
  determineArchitecturalGroup(node) {
    const type = node.type;

    // Simple type-based grouping
    switch (type) {
      case "External":
        return "external_systems";
      case "Frontend":
        return "client_application";
      case "Backend":
      case "Service":
        return "server_application";
      default:
        return "application_core";
    }
  }

  /**
   * Format advanced subgraph labels with context
   * @param {string} groupName - Raw group name
   * @param {number} nodeCount - Number of nodes in group
   * @returns {string} Formatted label
   */
  formatAdvancedSubgraphLabel(groupName, nodeCount) {
    const labelMap = {
      client_application: "Client Application",
      server_application: "Server Application",
      external_systems: "External Systems",
      application_core: "Application Core",
    };

    return (
      labelMap[groupName] ||
      groupName.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
    );
  }

  /**
   * Format subgraph label
   * @param {string} groupName - Raw group name
   * @returns {string} Formatted label
   */
  formatSubgraphLabel(groupName) {
    const labelMap = {
      Frontend: "Client-Side Application",
      Backend: "Server-Side Logic",
      Service: "Business Services",
      DataAccess: "Data Layer",
      External: "External Systems",
      Actor: "Users & Actors",
    };

    return labelMap[groupName] || groupName;
  }

  /**
   * Build the complete Mermaid diagram
   * @returns {string} Complete Mermaid diagram
   */
  build() {
    const lines = [];

    // Add header
    lines.push(this.diagramType);
    lines.push("");

    // Collect all nodes that belong to any subgraph (including nested ones)
    const nodesInSubgraphs = new Set();
    const processedSubgraphs = new Set();

    this.collectNodesInSubgraphs(nodesInSubgraphs);

    // Find standalone nodes (not in any subgraph)
    const standaloneNodes = [];
    this.nodeDefinitions.forEach((nodeDef, nodeId) => {
      if (!nodesInSubgraphs.has(nodeId)) {
        standaloneNodes.push(nodeDef);
      }
    });

    // Add standalone nodes first
    if (standaloneNodes.length > 0) {
      standaloneNodes.forEach((nodeDef) => {
        lines.push(`    ${nodeDef.definition}`);
      });
      lines.push("");
    }

    // Find top-level subgraphs (those not nested within others)
    const topLevelSubgraphs = this.findTopLevelSubgraphs();

    // Render top-level subgraphs with their nested hierarchy
    topLevelSubgraphs.forEach((subgraph) => {
      this.renderSubgraphHierarchy(subgraph, lines, 1, processedSubgraphs);
    });

    // Add cross-subgraph edges
    this.renderCrossSubgraphEdges(lines);

    return lines.join("\n");
  }

  /**
   * Collect all nodes that belong to subgraphs (including nested)
   * @param {Set} nodesInSubgraphs - Set to populate with node IDs
   */
  collectNodesInSubgraphs(nodesInSubgraphs) {
    this.subgraphDefinitions.forEach((subgraph) => {
      // Add direct nodes
      subgraph.nodes.forEach((nodeId) => {
        nodesInSubgraphs.add(nodeId);
      });

      // Add nodes from nested subgraphs
      if (subgraph.nestedSubgraphs) {
        subgraph.nestedSubgraphs.forEach((nestedId) => {
          const nestedSubgraph = this.subgraphDefinitions.get(nestedId);
          if (nestedSubgraph) {
            this.collectSubgraphNodes(nestedSubgraph, nodesInSubgraphs);
          }
        });
      }
    });
  }

  /**
   * Recursively collect nodes from a subgraph and its nested subgraphs
   * @param {object} subgraph - Subgraph definition
   * @param {Set} nodesInSubgraphs - Set to populate with node IDs
   */
  collectSubgraphNodes(subgraph, nodesInSubgraphs) {
    subgraph.nodes.forEach((nodeId) => {
      nodesInSubgraphs.add(nodeId);
    });

    if (subgraph.nestedSubgraphs) {
      subgraph.nestedSubgraphs.forEach((nestedId) => {
        const nestedSubgraph = this.subgraphDefinitions.get(nestedId);
        if (nestedSubgraph) {
          this.collectSubgraphNodes(nestedSubgraph, nodesInSubgraphs);
        }
      });
    }
  }

  /**
   * Find top-level subgraphs (those not nested within others)
   * @returns {Array} Array of top-level subgraph definitions
   */
  findTopLevelSubgraphs() {
    const nestedSubgraphIds = new Set();

    // Collect all nested subgraph IDs
    this.subgraphDefinitions.forEach((subgraph) => {
      if (subgraph.nestedSubgraphs) {
        subgraph.nestedSubgraphs.forEach((nestedId) => {
          nestedSubgraphIds.add(nestedId);
        });
      }
    });

    // Return subgraphs that are not nested within others
    const topLevel = [];
    this.subgraphDefinitions.forEach((subgraph) => {
      if (!nestedSubgraphIds.has(subgraph.id)) {
        topLevel.push(subgraph);
      }
    });

    return topLevel;
  }

  /**
   * Render a subgraph and its nested hierarchy
   * @param {object} subgraph - Subgraph definition
   * @param {Array} lines - Output lines array
   * @param {number} depth - Nesting depth for indentation
   * @param {Set} processedSubgraphs - Set of already processed subgraph IDs
   */
  renderSubgraphHierarchy(subgraph, lines, depth, processedSubgraphs) {
    if (processedSubgraphs.has(subgraph.id)) {
      return; // Avoid infinite recursion
    }
    processedSubgraphs.add(subgraph.id);

    const indent = "    ".repeat(depth);

    // Start subgraph
    lines.push(`${indent}subgraph ${subgraph.id}["${subgraph.label}"]`);

    // Add direct nodes
    if (subgraph.nodes && subgraph.nodes.length > 0) {
      subgraph.nodes.forEach((nodeId) => {
        const nodeDef = this.nodeDefinitions.get(nodeId);
        if (nodeDef) {
          lines.push(`${indent}    ${nodeDef.definition}`);
        }
      });

      if (subgraph.nestedSubgraphs && subgraph.nestedSubgraphs.length > 0) {
        lines.push(""); // Add spacing between direct nodes and nested subgraphs
      }
    }

    // Add nested subgraphs
    if (subgraph.nestedSubgraphs && subgraph.nestedSubgraphs.length > 0) {
      subgraph.nestedSubgraphs.forEach((nestedId) => {
        const nestedSubgraph = this.subgraphDefinitions.get(nestedId);
        if (nestedSubgraph) {
          this.renderSubgraphHierarchy(
            nestedSubgraph,
            lines,
            depth + 1,
            processedSubgraphs,
          );
        }
      });
    }

    // Add internal edges (edges within this subgraph or its nested subgraphs)
    const internalEdges = this.findInternalEdges(subgraph);
    if (internalEdges.length > 0) {
      lines.push(`${indent}    %% Internal edges`);
      internalEdges.forEach((edge) => {
        const sourceNode = this.nodeDefinitions.get(edge.source);
        const targetNode = this.nodeDefinitions.get(edge.target);
        if (sourceNode && targetNode && edge.label) {
          lines.push(
            `${indent}    ${edge.source}["${sourceNode.originalNode.label}"] -->|${edge.label}| ${edge.target}["${targetNode.originalNode.label}"]`,
          );
        } else {
          lines.push(`${indent}    ${edge.definition.replace(/^\s+/, "")}`);
        }
      });
    }

    // End subgraph
    lines.push(`${indent}end`);
    lines.push("");
  }

  /**
   * Find edges that are internal to a subgraph (including nested subgraphs)
   * @param {object} subgraph - Subgraph definition
   * @returns {Array} Array of internal edge definitions
   */
  findInternalEdges(subgraph) {
    const allSubgraphNodes = new Set();

    // Collect all nodes in this subgraph and its nested subgraphs
    this.collectSubgraphNodes(subgraph, allSubgraphNodes);

    // Find edges where both source and target are in this subgraph hierarchy
    return this.edgeDefinitions.filter(
      (edge) =>
        allSubgraphNodes.has(edge.source) && allSubgraphNodes.has(edge.target),
    );
  }

  /**
   * Render cross-subgraph edges
   * @param {Array} lines - Output lines array
   */
  renderCrossSubgraphEdges(lines) {
    const crossSubgraphEdges = this.edgeDefinitions.filter((edge) => {
      const sourceSubgraph = this.findNodeTopLevelSubgraph(edge.source);
      const targetSubgraph = this.findNodeTopLevelSubgraph(edge.target);
      return (
        !sourceSubgraph || !targetSubgraph || sourceSubgraph !== targetSubgraph
      );
    });

    if (crossSubgraphEdges.length > 0) {
      lines.push("    %% Cross-subgraph edges");
      const edgesBySource = new Map();
      crossSubgraphEdges.forEach((edge) => {
        if (!edgesBySource.has(edge.source)) {
          edgesBySource.set(edge.source, []);
        }
        edgesBySource.get(edge.source).push(edge);
      });

      edgesBySource.forEach((edges) => {
        edges.forEach((edge) => {
          const sourceNode = this.nodeDefinitions.get(edge.source);
          const targetNode = this.nodeDefinitions.get(edge.target);
          if (sourceNode && targetNode && edge.label) {
            lines.push(
              `    ${edge.source}["${sourceNode.originalNode.label}"] -->|${edge.label}| ${edge.target}["${targetNode.originalNode.label}"]`,
            );
          } else {
            lines.push(`    ${edge.definition.replace(/^\s+/, "")}`);
          }
        });
      });
    }
  }

  /**
   * Find the top-level subgraph that contains a node
   * @param {string} nodeId - Node ID
   * @returns {string|null} Top-level subgraph ID or null
   */
  findNodeTopLevelSubgraph(nodeId) {
    const topLevelSubgraphs = this.findTopLevelSubgraphs();

    for (const subgraph of topLevelSubgraphs) {
      const nodesInSubgraph = new Set();
      this.collectSubgraphNodes(subgraph, nodesInSubgraph);
      if (nodesInSubgraph.has(nodeId)) {
        return subgraph.id;
      }
    }

    return null;
  }

  /**
   * Add styling to the diagram
   * @param {object} styles - Style configuration
   * @returns {MermaidBuilder} For method chaining
   */
  addStyles(styles) {
    Object.entries(styles).forEach(([nodeType, style]) => {
      // Add class definition for node type
      this.styleDefinitions.push(`    classDef ${nodeType} ${style}`);
    });
    return this;
  }

  /**
   * Build diagram with architecture data
   * @param {object} architecture - Architecture data from LLM
   * @returns {string} Complete Mermaid diagram
   */
  buildFromArchitecture(architecture) {
    // Analyze and set optimal diagram direction for aspect ratio
    if (this.aspectRatioControl.enabled) {
      this.diagramType = this.analyzeOptimalDirection(architecture);
    }

    // Add nodes with consistent numbering and categorization
    const nodeIdMap = new Map();
    const nodesByType = new Map();

    architecture.nodes.forEach((node) => {
      // Generate numeric ID starting from 5000
      const numericId = this.generateId();
      const nodeConfig = {
        ...node,
        id: numericId,
      };

      this.addNode(nodeConfig);
      nodeIdMap.set(node.id, numericId);

      // Track nodes by type for better organization
      const nodeType = node.type || "Other";
      if (!nodesByType.has(nodeType)) {
        nodesByType.set(nodeType, []);
      }
      nodesByType.get(nodeType).push({ ...nodeConfig, originalId: node.id });
    });

    // Process hierarchical subgraphs with aspect ratio optimization
    if (architecture.subgraphs && architecture.subgraphs.length > 0) {
      this.processHierarchicalSubgraphs(architecture.subgraphs, nodeIdMap);
    } else {
      // Use compact subgraph generation if aspect ratio control is enabled
      if (
        this.aspectRatioControl.enabled &&
        this.aspectRatioControl.compactSubgraphs
      ) {
        this.generateCompactSubgraphs(
          architecture.nodes.map((node) => ({
            ...node,
            id: nodeIdMap.get(node.id),
          })),
        );
      } else {
        // Auto-generate subgraphs with enhanced logic
        this.autoGenerateEnhancedSubgraphs(
          architecture.nodes.map((node) => ({
            ...node,
            id: nodeIdMap.get(node.id),
          })),
        );
      }
    }

    // Add edges with enhanced labeling and organization
    this.addEnhancedEdges(architecture.edges, nodeIdMap);

    return this.build();
  }

  /**
   * Process hierarchical subgraphs with support for nested structures
   * @param {Array} subgraphs - Array of subgraph definitions
   * @param {Map} nodeIdMap - Map of original to numeric node IDs
   * @returns {MermaidBuilder} For method chaining
   */
  processHierarchicalSubgraphs(subgraphs, nodeIdMap) {
    // Create a map of subgraph ID to subgraph definition for easy lookup
    const subgraphMap = new Map();
    subgraphs.forEach((subgraph) => {
      subgraphMap.set(subgraph.id, subgraph);
    });

    // Find top-level subgraphs (those not nested within others) from the original architecture
    const nestedSubgraphIds = new Set();
    subgraphs.forEach((subgraph) => {
      if (subgraph.subgraphs && subgraph.subgraphs.length > 0) {
        subgraph.subgraphs.forEach((nestedId) => {
          nestedSubgraphIds.add(nestedId);
        });
      }
    });

    // Only process top-level subgraphs - recursion will handle nested ones
    const topLevelSubgraphs = subgraphs.filter(
      (subgraph) => !nestedSubgraphIds.has(subgraph.id),
    );

    topLevelSubgraphs.forEach((subgraph) => {
      this.processSubgraphHierarchy(subgraph, subgraphMap, nodeIdMap);
    });

    return this;
  }

  /**
   * Process a single subgraph and its nested structure
   * @param {object} subgraph - Subgraph definition
   * @param {Map} subgraphMap - Map of all subgraph definitions
   * @param {Map} nodeIdMap - Map of original to numeric node IDs
   * @returns {object} Processed subgraph definition
   */
  processSubgraphHierarchy(subgraph, subgraphMap, nodeIdMap) {
    const numericId = this.generateId();

    // Map original node IDs to generated numeric IDs
    const mappedNodes = (subgraph.nodes || [])
      .map((nodeId) => nodeIdMap.get(nodeId))
      .filter(Boolean);

    // Process nested subgraphs
    const nestedSubgraphs = [];
    if (subgraph.subgraphs && subgraph.subgraphs.length > 0) {
      subgraph.subgraphs.forEach((nestedSubgraphId) => {
        const nestedSubgraph = subgraphMap.get(nestedSubgraphId);
        if (nestedSubgraph) {
          const processedNested = this.processSubgraphHierarchy(
            nestedSubgraph,
            subgraphMap,
            nodeIdMap,
          );
          nestedSubgraphs.push(processedNested.id);
        }
      });
    }

    // Add this subgraph (including parent containers with no direct nodes)
    const processedSubgraph = {
      id: numericId,
      originalId: subgraph.id,
      label: this.enhanceSubgraphLabel(subgraph.label, mappedNodes.length),
      nodes: mappedNodes,
      nestedSubgraphs: nestedSubgraphs,
      isParentContainer: mappedNodes.length === 0 && nestedSubgraphs.length > 0,
    };

    this.subgraphDefinitions.set(numericId, processedSubgraph);

    return processedSubgraph;
  }

  /**
   * Auto-generate enhanced subgraphs with better architectural awareness
   * @param {Array} nodes - Array of nodes with mapped IDs
   * @returns {MermaidBuilder} For method chaining
   */
  autoGenerateEnhancedSubgraphs(nodes) {
    const subgraphGroups = new Map();
    const standaloneNodes = [];

    nodes.forEach((node) => {
      if (node.type === "Actor") {
        standaloneNodes.push(node.id);
        return;
      }

      // Determine the best architectural grouping
      const groupKey = this.determineEnhancedArchitecturalGroup(node);
      if (!subgraphGroups.has(groupKey)) {
        subgraphGroups.set(groupKey, []);
      }
      subgraphGroups.get(groupKey).push(node.id);
    });

    // Create subgraphs with enhanced organization
    subgraphGroups.forEach((nodeIds, groupName) => {
      if (nodeIds.length > 0) {
        this.addSubgraph({
          id: this.generateId(),
          label: this.formatAdvancedSubgraphLabel(groupName, nodeIds.length),
          nodes: nodeIds,
        });
      }
    });

    return this;
  }

  /**
   * Enhanced architectural grouping - simplified version
   * @param {object} node - Node to categorize
   * @returns {string} Group name
   */
  determineEnhancedArchitecturalGroup(node) {
    // Use the same simplified logic
    return this.determineArchitecturalGroup(node);
  }

  /**
   * Add edges with enhanced labeling and grouping
   * @param {Array} edges - Array of edges
   * @param {Map} nodeIdMap - Map of original to numeric node IDs
   * @returns {MermaidBuilder} For method chaining
   */
  addEnhancedEdges(edges, nodeIdMap) {
    // Group edges by source for better organization in output
    const edgesBySource = new Map();

    edges.forEach((edge) => {
      const sourceId = nodeIdMap.get(edge.source);
      const targetId = nodeIdMap.get(edge.target);

      if (sourceId && targetId) {
        if (!edgesBySource.has(sourceId)) {
          edgesBySource.set(sourceId, []);
        }

        edgesBySource.get(sourceId).push({
          ...edge,
          source: sourceId,
          target: targetId,
          label: this.enhanceEdgeLabel(edge.label),
        });
      }
    });

    // Add edges in organized groups
    edgesBySource.forEach((edgeGroup) => {
      edgeGroup.forEach((edge) => {
        this.addEdge(edge);
      });
    });

    return this;
  }

  /**
   * Enhance edge labels for clarity
   * @param {string} label - Original edge label
   * @returns {string} Enhanced edge label
   */
  enhanceEdgeLabel(label) {
    // Return labels as-is - no hardcoded transformations
    return label || "";
  }

  /**
   * Enhance subgraph labels with additional context
   * @param {string} originalLabel - Original subgraph label
   * @param {number} nodeCount - Number of nodes in subgraph
   * @returns {string} Enhanced label
   */
  enhanceSubgraphLabel(originalLabel, nodeCount) {
    // If the original label is already comprehensive, use it
    if (originalLabel && originalLabel.includes("<br>")) {
      return originalLabel;
    }

    // Otherwise enhance with architectural context
    return this.formatAdvancedSubgraphLabel(originalLabel, nodeCount);
  }
}

/**
 * Create a Mermaid diagram from architecture data
 * @param {object} architecture - Architecture data
 * @param {object} options - Builder options
 * @param {object} options.aspectRatioControl - Aspect ratio control settings
 * @param {boolean} options.aspectRatioControl.enabled - Enable aspect ratio optimization
 * @param {number} options.aspectRatioControl.preferredRatio - Preferred width:height ratio (default: 1.5)
 * @param {number} options.aspectRatioControl.maxWidth - Maximum nodes per row (default: 12)
 * @param {boolean} options.aspectRatioControl.autoDirection - Auto-select diagram direction
 * @param {boolean} options.aspectRatioControl.forceVertical - Force vertical layout
 * @param {boolean} options.aspectRatioControl.compactSubgraphs - Use compact subgraph organization
 * @returns {string} Complete Mermaid diagram
 */
export function createMermaidDiagram(architecture, options = {}) {
  const builder = new MermaidBuilder(options);
  return builder.buildFromArchitecture(architecture);
}

/**
 * Wrap Mermaid diagram in markdown code block
 * @param {string} mermaidCode - Raw Mermaid code
 * @param {object} options - Wrapping options
 * @returns {string} Markdown formatted diagram
 */
export function wrapInMarkdown(mermaidCode, options = {}) {
  const {
    title = "Architecture Diagram",
    description = "Generated architecture diagram showing system components and relationships.",
    includeMetadata = true,
  } = options;

  const lines = [];

  if (includeMetadata) {
    lines.push(`# ${title}`);
    lines.push("");
    lines.push(description);
    lines.push("");
    lines.push(
      `*Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}*`,
    );
    lines.push("");
  }

  lines.push("```mermaid");
  lines.push(mermaidCode);
  lines.push("```");

  if (includeMetadata) {
    lines.push("");
    lines.push("## Legend");
    lines.push(
      "- **Rectangles**: Application components, services, and modules",
    );
    lines.push("- **Rounded rectangles**: External systems and databases");
    lines.push("- **Circles**: Users and external actors");
    lines.push("- **Arrows**: Data flow and dependencies");
  }

  return lines.join("\n");
}
