import OpenAI from "openai";
import { readFileSync } from "fs";
import { fileURLToPath } from "url";
import { dirname, join } from "path";
import Mustache from "mustache";

/**
 * LLM Client wrapper fo
 */
export class LLMClient {
  constructor(config = {}) {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY || config.apiKey,
      ...config,
    });
  }

  /**
   * Generate architecture analysis from code summary
   * @param {object} projectData - Project data
   * @returns {object} Structured architecture data
   */
  async generateArchitecture(projectData) {
    console.log("🔍 Starting architectural analysis...");

    const userPrompt = this.createPrompt(projectData);

    try {
      // First pass: Initial architecture generation
      console.log("📝 First pass: Generating initial architecture...");
      const messages = [{ role: "user", content: userPrompt }];
      const initialResponse = await this.callLLM(messages);

      // Second pass: Verification and refinement
      // console.log("🔎 Second pass: Verifying and refining architecture...");
      // messages.push({ role: "assistant", content: initialResponse });
      // messages.push({
      //   role: "user",
      //   content: this.verificationPrompt(),
      // });

      const finalResponse = await this.callLLM(messages);
      const finalArchitecture = this.parseArchitectureResponse(finalResponse);

      console.log("✅ Architecture analysis completed with verification");
      return finalArchitecture;
    } catch (error) {
      console.error("Architecture generation failed:", error.message);
      throw new Error(`Failed to generate architecture: ${error.message}`);
    }
  }

  createPrompt(projectData) {
    const { metadata, files } = projectData;

    // Sort alphabetically and optimize tokens efficiency
    const csvFiles = this.filesToCsv(files);

    // Load the prompt template
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = dirname(__filename);
    const promptTemplate = readFileSync(
      join(__dirname, "prompt.mustache"),
      "utf8",
    );

    return Mustache.render(promptTemplate, {
      ast: csvFiles,
    });
  }

  /**
   * Optimize files for token efficiency
   * @param {object} files - Original files object
   * @returns {string} CSV format of files data
   */
  filesToCsv(files) {
    // Sort files alphabetically by file path
    const sortedFileEntries = Object.entries(files).sort(([pathA], [pathB]) =>
      pathA.localeCompare(pathB),
    );

    // Convert to CSV format: path|imports|exports
    const csvLines = ["path|imports|exports"]; // Header

    for (const [_, fileData] of sortedFileEntries) {
      const path = fileData.path || "";
      const imports = Array.isArray(fileData.imports)
        ? fileData.imports.join(",")
        : "";
      const exports = Array.isArray(fileData.exports)
        ? fileData.exports.join(",")
        : "";

      csvLines.push(`${path}|${imports}|${exports}`);
    }

    return csvLines.join("\n");
  }

  /**
   * Call the OpenAI API
   * @param {Array|string} messagesOrPrompt - Array of messages or single prompt string
   * @returns {string} Raw response text
   */
  async callLLM(messagesOrPrompt) {
    const messages = Array.isArray(messagesOrPrompt)
      ? messagesOrPrompt
      : [{ role: "user", content: messagesOrPrompt }];

    console.log("Calling LLM with", messages.length, "messages");

    const client = new OpenAI({
      apiKey: process.env.GEMINI_API_KEY,
      baseURL: "https://generativelanguage.googleapis.com/v1beta/openai/",
    });

    const response = await client.chat.completions.create({
      model: "gemini-2.5-pro",
      response_format: { type: "json_object" },
      messages: messages,
    });

    return response.choices[0].message.content;
  }

  /**
   * Parse and validate the LLM response
   * @param {string} response - Raw LLM response
   * @returns {object} Parsed architecture data
   */
  parseArchitectureResponse(response) {
    try {
      const architecture = JSON.parse(response);

      // Basic validation
      if (!architecture.nodes?.length) {
        throw new Error("Response must include at least one node");
      }
      if (!Array.isArray(architecture.edges)) {
        architecture.edges = [];
      }
      if (!Array.isArray(architecture.subgraphs)) {
        architecture.subgraphs = [];
      }

      // Validate nodes have required fields
      const nodeIds = new Set();
      architecture.nodes.forEach((node) => {
        if (!node.id || !node.label) {
          throw new Error(`Invalid node: missing id or label`);
        }
        nodeIds.add(node.id);
      });

      // Validate edges reference existing nodes
      architecture.edges.forEach((edge) => {
        if (!nodeIds.has(edge.source) || !nodeIds.has(edge.target)) {
          throw new Error(`Invalid edge: references non-existent node`);
        }
      });

      return architecture;
    } catch (error) {
      console.error("Failed to parse LLM response:", error.message);
      throw new Error(`Invalid architecture format: ${error.message}`);
    }
  }

  /**
   * Test the LLM connection
   * @returns {boolean} True if connection successful
   */
  async testConnection() {
    try {
      const response = await this.callLLM(
        "Return only valid JSON",
        'Respond with: {"status": "ok"}',
      );
      const result = JSON.parse(response);
      return result.status === "ok";
    } catch (error) {
      console.error("LLM connection test failed:", error.message);
      return false;
    }
  }

  /**
   * Create verification message for second pass
   * @returns {string} Verification message
   */
  verificationPrompt() {
    return `Let's double check and make sure we didn't miss any important details.

Please review your architecture analysis and verify:

1. **Architecture Type Detection**: Is this correctly identified as Next.js, Turborepo/Monorepo, or other? Check for:
   - Presence of /apps, /packages directories (indicates monorepo)
   - Single /app or /pages directory (indicates Next.js)
   - Multiple applications vs single application

2. **Component Coverage**: Are all important components represented?
   - Check the file paths in the AST - did we miss any major directories?
   - Are there database connections, API integrations, or external services not captured?
   - Look for config files, environment setups, or deployment scripts that indicate missing services

3. **Subgraph Structure**: Is the grouping correct?
   - For monorepos: Should have Applications, Packages, External Services
   - For Next.js: Should have Application, External Services
   - Are nodes properly categorized in the right subgraphs?

4. **Missing Relationships**: Are there important connections we missed?
   - Check import statements - do they suggest missing edges?
   - Are there API calls or database connections not represented?
   - Look for authentication flows, data flows, or service dependencies

5. **External Systems**: Did we identify all external dependencies?
   - Database systems (PostgreSQL, MongoDB, etc.)
   - APIs (OpenAI, Stripe, Auth providers, etc.)
   - Infrastructure (Redis, S3, CDNs, etc.)
   - Third-party services from package.json or imports

6. **Node Labels**: Are they descriptive and following the format "Component Name<br>Technology Stack"?

7. **User Actor**: Is there a standalone "User<br>External Actor" node properly connected to entry points?

Based on this review, provide a REVISED and IMPROVED architecture graph that addresses any issues found. If the initial analysis was correct, you may return the same structure, but ensure it's complete and accurate. Return the same JSON schema format as before.

IMPORTANT: Make sure this is still readable by a mid-level developer. Don't overcomplicate it.`;
  }
}

/**
 * Create LLM client with OpenAI
 * @param {object} config - Client configuration
 * @returns {LLMClient} Configured LLM client
 */
export function createLLMClient(config = {}) {
  if (!process.env.OPENAI_API_KEY && !config.apiKey) {
    throw new Error(
      "No OpenAI API key found. Set OPENAI_API_KEY environment variable.",
    );
  }

  return new LLMClient(config);
}
