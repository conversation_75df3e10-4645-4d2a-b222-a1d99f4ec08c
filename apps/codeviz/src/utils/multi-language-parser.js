import { parse } from "@babel/parser";
import _traverse from "@babel/traverse";
const traverse = _traverse.default || _traverse;
import * as t from "@babel/types";
import fs from "fs-extra";
import path from "path";
import gitignore from "ignore";

// Tree-sitter imports
import Parser from "tree-sitter";
import Python from "tree-sitter-python";
import Go from "tree-sitter-go";
import Ruby from "tree-sitter-ruby";
import Rust from "tree-sitter-rust";

/**
 * Supported file extensions and their corresponding languages
 */
const LANGUAGE_CONFIG = {
  // Frontend languages (using Babel)
  ".js": { parser: "babel", language: "javascript" },
  ".jsx": { parser: "babel", language: "javascript" },
  ".ts": { parser: "babel", language: "typescript" },
  ".tsx": { parser: "babel", language: "typescript" },
  ".mjs": { parser: "babel", language: "javascript" },
  ".cjs": { parser: "babel", language: "javascript" },

  // HTML/CSS
  ".html": { parser: "regex", language: "html" },
  ".htm": { parser: "regex", language: "html" },
  ".css": { parser: "regex", language: "css" },
  ".scss": { parser: "regex", language: "css" },
  ".sass": { parser: "regex", language: "css" },
  ".less": { parser: "regex", language: "css" },

  // Backend languages (using Tree-sitter)
  ".py": { parser: "tree-sitter", language: "python", treeSitterLang: Python },
  ".pyi": { parser: "tree-sitter", language: "python", treeSitterLang: Python },
  ".go": { parser: "tree-sitter", language: "go", treeSitterLang: Go },
  ".rb": { parser: "tree-sitter", language: "ruby", treeSitterLang: Ruby },
  ".rs": { parser: "tree-sitter", language: "rust", treeSitterLang: Rust },
};

/**
 * Get all supported extensions
 */
export const SUPPORTED_EXTENSIONS = Object.keys(LANGUAGE_CONFIG);

/**
 * Default patterns to ignore
 */
const DEFAULT_IGNORE_PATTERNS = [
  "node_modules/**",
  ".git/**",
  "dist/**",
  "build/**",
  "coverage/**",
  ".next/**",
  ".nuxt/**",
  "out/**",
  "target/**", // Rust
  "__pycache__/**", // Python
  "*.pyc",
  "*.pyo",
  ".pytest_cache/**",
  "venv/**",
  "env/**",
  ".venv/**",
  ".env/**",
  "bin/**",
  "obj/**", // C#
  ".gradle/**", // Java/Kotlin
  "vendor/**", // PHP/Ruby
  "*.min.js",
  "*.bundle.js",
];

/**
 * Language-specific parser instances
 */
const treeSitterParsers = {};

/**
 * Initialize tree-sitter parser for a specific language
 */
function getTreeSitterParser(language, treeSitterLang) {
  if (!treeSitterParsers[language]) {
    try {
      const parser = new Parser();
      parser.setLanguage(treeSitterLang);
      treeSitterParsers[language] = parser;
    } catch (error) {
      console.warn(
        `Failed to initialize tree-sitter parser for ${language}:`,
        error.message
      );
      return null;
    }
  }
  return treeSitterParsers[language];
}

/**
 * Get Babel parser options based on file extension
 */
function getBabelParserOptions(filePath, content) {
  const isTypeScript = /\.tsx?$/.test(filePath);
  const isJSX =
    /\.(jsx|tsx)$/.test(filePath) ||
    (content.includes("<") &&
      content.includes(">") &&
      content.includes("return"));

  return {
    sourceType: "module",
    allowImportExportEverywhere: true,
    allowReturnOutsideFunction: true,
    plugins: [
      "asyncGenerators",
      "bigInt",
      "classProperties",
      "decorators-legacy",
      "doExpressions",
      "dynamicImport",
      "exportDefaultFrom",
      "exportNamespaceFrom",
      "functionBind",
      "functionSent",
      "importMeta",
      "nullishCoalescingOperator",
      "numericSeparator",
      "objectRestSpread",
      "optionalCatchBinding",
      "optionalChaining",
      "throwExpressions",
      "topLevelAwait",
      "trailingFunctionCommas",
      ...(isTypeScript ? ["typescript"] : []),
      ...(isJSX ? ["jsx"] : []),
    ],
  };
}

/**
 * Extract imports from Babel AST (JavaScript/TypeScript)
 */
function extractBabelImports(ast) {
  const imports = [];

  traverse(ast, {
    ImportDeclaration(path) {
      const source = path.node.source.value;
      imports.push(source);
    },

    // Handle dynamic imports
    CallExpression(path) {
      if (t.isImport(path.node.callee) && path.node.arguments.length > 0) {
        const arg = path.node.arguments[0];
        if (t.isStringLiteral(arg)) {
          imports.push(arg.value);
        }
      }
    },

    // Handle require() calls
    VariableDeclarator(path) {
      if (
        t.isCallExpression(path.node.init) &&
        t.isIdentifier(path.node.init.callee, { name: "require" })
      ) {
        const arg = path.node.init.arguments[0];
        if (t.isStringLiteral(arg)) {
          imports.push(arg.value);
        }
      }
    },
  });

  return imports;
}

/**
 * Extract exports from Babel AST (JavaScript/TypeScript)
 */
function extractBabelExports(ast) {
  const exports = [];

  traverse(ast, {
    ExportDefaultDeclaration(path) {
      const node = path.node.declaration;

      if (t.isIdentifier(node)) {
        exports.push(node.name);
      } else if (t.isFunctionDeclaration(node) || t.isClassDeclaration(node)) {
        exports.push(node.id?.name || "default");
      } else {
        exports.push("default");
      }
    },

    ExportNamedDeclaration(path) {
      if (path.node.declaration) {
        const decl = path.node.declaration;
        if (t.isVariableDeclaration(decl)) {
          decl.declarations.forEach((declarator) => {
            if (t.isIdentifier(declarator.id)) {
              exports.push(declarator.id.name);
            }
          });
        } else if (
          t.isFunctionDeclaration(decl) ||
          t.isClassDeclaration(decl)
        ) {
          exports.push(decl.id.name);
        }
      } else if (path.node.specifiers) {
        path.node.specifiers.forEach((spec) => {
          if (t.isExportSpecifier(spec)) {
            exports.push(spec.exported.name);
          }
        });
      }
    },
  });

  return exports;
}

/**
 * Extract imports and exports from Tree-sitter AST
 */
function extractTreeSitterImportsExports(tree, language) {
  const imports = [];
  const exports = [];
  const functions = [];
  const classes = [];

  // Walk the tree and extract relevant information
  function walkNode(node) {
    switch (language) {
      case "python":
        extractPythonNodes(node, imports, exports, functions, classes);
        break;
      case "go":
        extractGoNodes(node, imports, exports, functions, classes);
        break;
      case "ruby":
        extractRubyNodes(node, imports, exports, functions, classes);
        break;
      case "rust":
        extractRustNodes(node, imports, exports, functions, classes);
        break;
    }

    // Recursively walk children
    for (let i = 0; i < node.childCount; i++) {
      walkNode(node.child(i));
    }
  }

  walkNode(tree.rootNode);

  return { imports, exports, functions, classes };
}

/**
 * Extract Python imports, exports, functions, and classes
 */
function extractPythonNodes(node, imports, exports, functions, classes) {
  const nodeType = node.type;

  if (nodeType === "import_statement" || nodeType === "import_from_statement") {
    const text = node.text;
    if (text.startsWith("import ") || text.startsWith("from ")) {
      imports.push(text.trim());
    }
  } else if (nodeType === "function_definition") {
    const nameNode = node.childForFieldName("name");
    if (nameNode) {
      functions.push(nameNode.text);
    }
  } else if (nodeType === "class_definition") {
    const nameNode = node.childForFieldName("name");
    if (nameNode) {
      classes.push(nameNode.text);
    }
  }
}


/**
 * Extract Go imports, exports, functions, and classes
 */
function extractGoNodes(node, imports, exports, functions, classes) {
  const nodeType = node.type;

  if (nodeType === "import_spec") {
    imports.push(node.text.trim());
  } else if (nodeType === "function_declaration") {
    const nameNode = node.childForFieldName("name");
    if (nameNode) {
      const funcName = nameNode.text;
      functions.push(funcName);
      // Go exports functions that start with uppercase
      if (funcName[0] === funcName[0].toUpperCase()) {
        exports.push(funcName);
      }
    }
  } else if (nodeType === "type_declaration") {
    const nameNode = node.childForFieldName("name");
    if (nameNode) {
      const typeName = nameNode.text;
      classes.push(typeName);
      // Go exports types that start with uppercase
      if (typeName[0] === typeName[0].toUpperCase()) {
        exports.push(typeName);
      }
    }
  }
}


/**
 * Extract Ruby imports, exports, functions, and classes
 */
function extractRubyNodes(node, imports, exports, functions, classes) {
  const nodeType = node.type;

  if (nodeType === "call" && node.text.startsWith("require")) {
    imports.push(node.text.trim());
  } else if (nodeType === "class") {
    const nameNode = node.childForFieldName("name");
    if (nameNode) {
      classes.push(nameNode.text);
      exports.push(nameNode.text);
    }
  } else if (nodeType === "method") {
    const nameNode = node.childForFieldName("name");
    if (nameNode) {
      functions.push(nameNode.text);
    }
  }
}

/**
 * Extract Rust imports, exports, functions, and classes
 */
function extractRustNodes(node, imports, exports, functions, classes) {
  const nodeType = node.type;

  if (nodeType === "use_declaration") {
    imports.push(node.text.trim());
  } else if (nodeType === "function_item") {
    const nameNode = node.childForFieldName("name");
    if (nameNode) {
      const funcName = nameNode.text;
      functions.push(funcName);
      // Rust exports public functions
      if (node.text.includes("pub ")) {
        exports.push(funcName);
      }
    }
  } else if (
    nodeType === "struct_item" ||
    nodeType === "enum_item" ||
    nodeType === "trait_item"
  ) {
    const nameNode = node.childForFieldName("name");
    if (nameNode) {
      const typeName = nameNode.text;
      classes.push(typeName);
      // Rust exports public types
      if (node.text.includes("pub ")) {
        exports.push(typeName);
      }
    }
  }
}

/**
 * Extract imports and exports using regex patterns (fallback method)
 */
function extractWithRegex(content, language) {
  const imports = [];
  const exports = [];
  const functions = [];
  const classes = [];

  const patterns = getRegexPatterns(language);

  // Extract imports
  if (patterns.imports) {
    patterns.imports.forEach((pattern) => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach((match) => imports.push(match.trim()));
      }
    });
  }

  // Extract exports/functions/classes
  if (patterns.exports) {
    patterns.exports.forEach((pattern) => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach((match) => exports.push(match.trim()));
      }
    });
  }

  if (patterns.functions) {
    patterns.functions.forEach((pattern) => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach((match) => functions.push(match.trim()));
      }
    });
  }

  if (patterns.classes) {
    patterns.classes.forEach((pattern) => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach((match) => classes.push(match.trim()));
      }
    });
  }

  return { imports, exports, functions, classes };
}

/**
 * Get regex patterns for different languages
 */
function getRegexPatterns(language) {
  const patterns = {
    html: {
      imports: [/<link[^>]*href=["'][^"']*["']/g, /<script[^>]*src=["'][^"']*["']/g],
      exports: [],
      functions: [/<script[^>]*>([\s\S]*?)<\/script>/g],
      classes: [/class=["']([^"']*)["']/g, /id=["']([^"']*)["']/g],
    },
    css: {
      imports: [/@import\s+["'][^"']*["']/g, /@import\s+url\([^)]*\)]/g],
      exports: [],
      functions: [],
      classes: [/\.[a-zA-Z][\w-]*/g, /#[a-zA-Z][\w-]*/g],
    },
    python: {
      imports: [/import\s+[\w.]+/g, /from\s+[\w.]+\s+import/g],
      functions: [/def\s+(\w+)/g],
      classes: [/class\s+(\w+)/g],
    },
    go: {
      imports: [/import\s+"[\w\/.-]+"/g, /import\s+[\w]+\s+"[\w\/.-]+"/g],
      functions: [/func\s+(\w+)/g],
      classes: [/type\s+(\w+)\s+struct/g, /type\s+(\w+)\s+interface/g],
    },
    ruby: {
      imports: [
        /require\s+['"][\w\/.-]+['"]/g,
        /require_relative\s+['"][\w\/.-]+['"]/g,
      ],
      functions: [/def\s+(\w+)/g],
      classes: [/class\s+(\w+)/g, /module\s+(\w+)/g],
    },
    rust: {
      imports: [/use\s+[\w:]+/g],
      functions: [/fn\s+(\w+)/g, /pub\s+fn\s+(\w+)/g],
      classes: [
        /struct\s+(\w+)/g,
        /enum\s+(\w+)/g,
        /trait\s+(\w+)/g,
        /impl\s+(\w+)/g,
      ],
    },
  };

  return patterns[language] || {};
}

/**
 * Parse a single file and extract relevant information
 */
export function parseFile(filePath, content) {
  const ext = path.extname(filePath);
  const config = LANGUAGE_CONFIG[ext];

  if (!config) {
    console.warn(`Unsupported file extension: ${ext}`);
    return {
      path: filePath,
      imports: [],
      exports: [],
      functions: [],
      classes: [],
      language: "unknown",
      error: `Unsupported file extension: ${ext}`,
    };
  }

  try {
    let imports = [];
    let exports = [];
    let functions = [];
    let classes = [];

    if (config.parser === "babel") {
      // Use Babel for JavaScript/TypeScript
      const options = getBabelParserOptions(filePath, content);
      const ast = parse(content, options);

      imports = extractBabelImports(ast);
      exports = extractBabelExports(ast);

      // Extract functions and classes from Babel AST
      traverse(ast, {
        FunctionDeclaration(path) {
          if (path.node.id) {
            functions.push(path.node.id.name);
          }
        },
        ClassDeclaration(path) {
          if (path.node.id) {
            classes.push(path.node.id.name);
          }
        },
      });
    } else if (config.parser === "tree-sitter") {
      // Use Tree-sitter for backend languages
      const parser = getTreeSitterParser(
        config.language,
        config.treeSitterLang
      );

      if (parser) {
        try {
          const tree = parser.parse(content);
          const result = extractTreeSitterImportsExports(tree, config.language);

          imports = result.imports;
          exports = result.exports;
          functions = result.functions;
          classes = result.classes;
        } catch (error) {
          console.warn(
            `Tree-sitter parsing failed for ${config.language}, falling back to regex:`,
            error.message
          );
          // Fallback to regex parsing
          const result = extractWithRegex(content, config.language);
          imports = result.imports;
          exports = result.exports;
          functions = result.functions;
          classes = result.classes;
        }
      } else {
        // Fallback to regex parsing when tree-sitter initialization fails
        console.warn(`Using regex fallback for ${config.language}`);
        const result = extractWithRegex(content, config.language);
        imports = result.imports;
        exports = result.exports;
        functions = result.functions;
        classes = result.classes;
      }
    } else if (config.parser === "regex") {
      // Use regex patterns as fallback
      const result = extractWithRegex(content, config.language);

      imports = result.imports;
      exports = result.exports;
      functions = result.functions;
      classes = result.classes;
    }

    return {
      path: filePath,
      imports,
      exports,
      functions,
      classes,
      language: config.language,
    };
  } catch (error) {
    console.warn(`Failed to parse ${filePath}:`, error.message);
    return {
      path: filePath,
      imports: [],
      exports: [],
      functions: [],
      classes: [],
      language: config.language,
      error: error.message,
    };
  }
}

/**
 * Get files to parse from a directory
 */
export async function getFilesToParse(rootPath) {
  const ig = (gitignore.default || gitignore)();

  // Load .gitignore if it exists
  try {
    const gitignorePath = path.join(rootPath, ".gitignore");
    const gitignoreContent = await fs.readFile(gitignorePath, "utf8");
    ig.add(gitignoreContent);
  } catch (error) {
    // .gitignore doesn't exist, continue
  }

  // Add default ignore patterns
  ig.add(DEFAULT_IGNORE_PATTERNS);

  const files = [];

  async function walkDirectory(dir, relativePath = "") {
    const entries = await fs.readdir(dir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      const relativeFilePath = path
        .join(relativePath, entry.name)
        .replace(/\\/g, "/");

      // Skip ignored files/directories
      if (ig.ignores(relativeFilePath)) {
        continue;
      }

      if (entry.isDirectory()) {
        await walkDirectory(fullPath, relativeFilePath);
      } else if (entry.isFile()) {
        const ext = path.extname(entry.name);
        if (SUPPORTED_EXTENSIONS.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
  }

  await walkDirectory(rootPath);
  return files;
}

/**
 * Get language statistics from parsed files
 */
export function getLanguageStats(parsedFiles) {
  const stats = {};

  parsedFiles.forEach((file) => {
    const lang = file.language || "unknown";
    if (!stats[lang]) {
      stats[lang] = {
        fileCount: 0,
        totalImports: 0,
        totalExports: 0,
        totalFunctions: 0,
        totalClasses: 0,
      };
    }

    stats[lang].fileCount++;
    stats[lang].totalImports += file.imports.length;
    stats[lang].totalExports += file.exports.length;
    stats[lang].totalFunctions += file.functions?.length || 0;
    stats[lang].totalClasses += file.classes?.length || 0;
  });

  return stats;
}
