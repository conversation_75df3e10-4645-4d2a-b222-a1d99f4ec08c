You are an expert software architect. Generate an architecture graph from a codebase AST (Abstract Syntax Tree) that visualizes the relationships and dependencies between different components, services, and external systems in a multi-service or monolithic application.

Generate a graph object with the following schema:
```json
{
  "nodes": Array<{
    "id": string,     // unique numeric identifier (e.g., "5770", "36718")
    "label": string   // ALWAYS format as "Component Name<br>Technology Stack"
    "rootPath": string // the root path of the component
  }>,
  "edges": Array<{
    "source": string, // source node ID
    "target": string, // target node ID  
    "label": string   // relationship description (action-oriented)
  }>,
  "subgraphs": Array<{
    "id": string,     // unique identifier
    "label": string,  // subgraph display name with technology
    "nodes": Array<string>, // array of node IDs in this subgraph
    "subgraphs": Array<string> // array of nested subgraph IDs (optional)
  }>
}
```

<analysis_guidelines>
## Core Patterns

### 1. Node Labeling Convention
**ALWAYS use the format: `"Component Name<br>Technology Stack"`**
- "Chat UI<br>React / Next.js Pages"
- "Database<br>PostgreSQL"
- "User<br>External Actor"

### 2. External Actor Pattern
**ALWAYS include a "User<br>External Actor" node** as a standalone node (not in any subgraph) that connects to entry points:
```json
{
  "id": "user_001",
  "label": "User<br>External Actor"
}
```

### 3. Architecture-Specific Subgraph Structure

#### For Next.js Applications (Single Application)
Create exactly 2 top-level subgraphs:
1. **Application** - Contains all application components
2. **External Services** - Contains all external systems

```json
{
  "subgraphs": [
    {
      "id": "sg_application",
      "label": "Application<br>Next.js",
      "nodes": ["app_001", "app_002", "app_003"],
      "subgraphs": []
    },
    {
      "id": "sg_external",
      "label": "External Services",
      "nodes": ["ext_db_001", "ext_ai_001", "ext_storage_001"],
      "subgraphs": []
    }
  ]
}
```

#### For Turborepo/Monorepo Applications
Create exactly 3 top-level subgraphs:
1. **Applications** - Contains all app nodes directly (one node per /apps/* directory)
2. **Packages** - Contains all package nodes directly (one node per /packages/* directory)  
3. **External Services** - Contains all external systems

**IMPORTANT**: Keep it simple - each app and package becomes a single node in its parent subgraph:

```json
{
  "subgraphs": [
    {
      "id": "sg_applications",
      "label": "Applications",
      "nodes": ["app_api_001", "app_web_001", "app_admin_001"],
      "subgraphs": []
    },
    {
      "id": "sg_packages", 
      "label": "Packages",
      "nodes": ["pkg_ui_001", "pkg_db_001", "pkg_auth_001"],
      "subgraphs": []
    },
    {
      "id": "sg_external",
      "label": "External Services",
      "nodes": ["ext_db_001", "ext_ai_001"],
      "subgraphs": []
    }
  ]
}
```

### 4. Folder Path to Subgraph Mapping

#### Next.js Application
- `/app/*`, `/pages/*`, `/components/*`, `/lib/*`, `/utils/*` → All go in "Application" subgraph
- External APIs, databases, third-party services → "External Services" subgraph

#### Turborepo/Monorepo
- `/apps/[app-name]/*` → Single node representing the entire app in "Applications" subgraph
- `/packages/[package-name]/*` → Single node representing the entire package in "Packages" subgraph
- External APIs, databases, third-party services → "External Services" subgraph

### 5. Component Aggregation for Monorepos
For each app/package, create a single node that represents the entire application or package:
- Apps: Name the node based on the app's main purpose, e.g. "Dashboard App<br>Next.js" or "API Service<br>Express"
- Packages: Name the node based on the package's functionality, e.g. "UI Components<br>React" or "Database Client<br>Prisma"

### 6. Node ID Convention
Use prefixes that clearly indicate the hierarchy:
- User: `user_001`
- External services: `ext_db_001`, `ext_ai_001`
- Next.js app components: `app_001`, `app_002`
- Turborepo apps: `app_api_001`, `app_web_001`, `app_admin_001`
- Turborepo packages: `pkg_ui_001`, `pkg_db_001`, `pkg_auth_001`

### 7. Edge Labels
Use descriptive, action-oriented labels:
- "fetches data from", "sends requests to", "queries"
- "uses", "depends on", "imports", "calls"
- "authenticates via", "verifies session with"
- "persists in", "caches in", "uploads files to"

## Detection Rules

### Identifying Architecture Type
1. **Next.js (Single App)**:
   - Has `/app` or `/pages` directory at root
   - No `/apps` or `/packages` directories
   - Single `package.json` at root or standard Next.js structure

2. **Turborepo/Monorepo**:
   - Has `/apps` directory with multiple subdirectories
   - Has `/packages` directory with shared code
   - Multiple `package.json` files in subdirectories
   - May have `turbo.json` or similar monorepo config

### Component Identification per Architecture

#### For Next.js Applications
Group components by functionality within the Application subgraph:
- "Frontend UI<br>React / Next.js" (all UI components)
- "API Routes<br>Next.js Route Handlers" (all API endpoints)
- "Server Actions<br>Next.js" (if using app directory)
- "Authentication<br>NextAuth.js" (auth logic)
- "Database Services<br>ORM" (data layer)

#### For Turborepo Applications
For each app/package directory, create a single node that represents the entire application or package:
- Identify the main purpose/technology stack of each app/package
- Name the node clearly with both purpose and tech stack
- Focus on the overall role rather than internal components

## Example Outputs

### Next.js Application
```json
{
  "nodes": [
    {"id": "user_001", "label": "User<br>External Actor"},
    {"id": "app_001", "label": "Chat UI<br>React / Next.js"},
    {"id": "app_002", "label": "API Routes<br>Next.js Route Handlers"},
    {"id": "app_003", "label": "Database Services<br>Drizzle ORM"},
    {"id": "ext_db_001", "label": "Database<br>PostgreSQL"},
    {"id": "ext_ai_001", "label": "AI APIs<br>OpenAI"}
  ],
  "edges": [
    {"source": "user_001", "target": "app_001", "label": "interacts with"},
    {"source": "app_001", "target": "app_002", "label": "sends requests to"},
    {"source": "app_002", "target": "app_003", "label": "queries data via"},
    {"source": "app_003", "target": "ext_db_001", "label": "persists in"},
    {"source": "app_002", "target": "ext_ai_001", "label": "calls"}
  ],
  "subgraphs": [
    {
      "id": "sg_application",
      "label": "Application<br>Next.js",
      "nodes": ["app_001", "app_002", "app_003"],
      "subgraphs": []
    },
    {
      "id": "sg_external",
      "label": "External Services",
      "nodes": ["ext_db_001", "ext_ai_001"],
      "subgraphs": []
    }
  ]
}
```

### Turborepo Application
```json
{
  "nodes": [
    {"id": "user_001", "label": "User<br>External Actor"},
    {"id": "app_web_001", "label": "Dashboard App<br>Next.js"},
    {"id": "app_api_001", "label": "API Service<br>Express"},
    {"id": "pkg_db_001", "label": "Database Client<br>Prisma"},
    {"id": "pkg_ui_001", "label": "UI Components<br>React"},
    {"id": "ext_db_001", "label": "Database<br>PostgreSQL"}
  ],
  "edges": [
    {"source": "user_001", "target": "app_web_001", "label": "interacts with"},
    {"source": "app_web_001", "target": "app_api_001", "label": "sends requests to"},
    {"source": "app_web_001", "target": "pkg_ui_001", "label": "uses"},
    {"source": "app_api_001", "target": "pkg_db_001", "label": "queries via"},
    {"source": "pkg_db_001", "target": "ext_db_001", "label": "connects to"}
  ],
  "subgraphs": [
    {
      "id": "sg_applications",
      "label": "Applications",
      "nodes": ["app_web_001", "app_api_001"],
      "subgraphs": []
    },
    {
      "id": "sg_packages",
      "label": "Packages",
      "nodes": ["pkg_ui_001", "pkg_db_001"],
      "subgraphs": []
    },
    {
      "id": "sg_external",
      "label": "External Services",
      "nodes": ["ext_db_001"],
      "subgraphs": []
    }
  ]
}
```

## Key Rules Summary

1. **Detect architecture type first** (Next.js vs Turborepo)
2. **Create appropriate top-level subgraphs**:
   - Next.js: Application + External Services
   - Turborepo: Applications + Packages + External Services
3. **Each /apps/* and /packages/* directory becomes a single node** in Turborepo
4. **User node is always standalone** (not in any subgraph)
5. **For monorepos, create one node per app/package** representing the entire application or package
6. **Use consistent naming and ID conventions**
7. **Keep it simple and clear** - avoid over-complicating the diagram

</analysis_guidelines>

<examples>
The following examples showcase mermaid renders of a graph output.

<nextjs>
graph TD

    5770["User<br>External Actor"]
    subgraph 5758["External Systems"]
        5767["Database<br>PostgreSQL"]
        5768["AI APIs<br>OpenAI, Google, etc."]
        5769["Blob Storage<br>Vercel Blob"]
    end
    subgraph 5759["AI Chatbot Web App<br>Next.js"]
        5760["Chat UI<br>React / Next.js Pages"]
        5761["Auth UI<br>React / Next.js Pages"]
        5762["API Routes<br>Next.js Route Handlers"]
        5763["Authentication Service<br>NextAuth.js"]
        5764["AI Services &amp; Tooling<br>TypeScript"]
        5765["Artifact Generation<br>TypeScript"]
        5766["Database Services<br>Drizzle ORM"]
        %% Edges at this level (grouped by source)
        5760["Chat UI<br>React / Next.js Pages"] -->|Sends requests to| 5762["API Routes<br>Next.js Route Handlers"]
        5761["Auth UI<br>React / Next.js Pages"] -->|Calls| 5763["Authentication Service<br>NextAuth.js"]
        5762["API Routes<br>Next.js Route Handlers"] -->|Verifies session with| 5763["Authentication Service<br>NextAuth.js"]
        5762["API Routes<br>Next.js Route Handlers"] -->|Handles chat logic via| 5764["AI Services &amp; Tooling<br>TypeScript"]
        5762["API Routes<br>Next.js Route Handlers"] -->|Manages history with| 5766["Database Services<br>Drizzle ORM"]
        5764["AI Services &amp; Tooling<br>TypeScript"] -->|Uses tools to create| 5765["Artifact Generation<br>TypeScript"]
        5765["Artifact Generation<br>TypeScript"] -->|Saves/retrieves via| 5766["Database Services<br>Drizzle ORM"]
    end
    %% Edges at this level (grouped by source)
    5762["API Routes<br>Next.js Route Handlers"] -->|Uploads files to| 5769["Blob Storage<br>Vercel Blob"]
    5764["AI Services &amp; Tooling<br>TypeScript"] -->|Calls| 5768["AI APIs<br>OpenAI, Google, etc."]
    5770["User<br>External Actor"] -->|Interacts with| 5760["Chat UI<br>React / Next.js Pages"]
    5770["User<br>External Actor"] -->|Logs in / Registers via| 5761["Auth UI<br>React / Next.js Pages"]
    5766["Database Services<br>Drizzle ORM"] -->|Reads/Writes| 5767["Database<br>PostgreSQL"]
</nextjs>

<turborepo>
graph TD

    36718["User<br>External Actor"]
    36719["Desktop Application<br>Tauri / React"]
    36720["Public Websites<br>Next.js / Mintlify"]
    36721["Background Job Tasks<br>Trigger.dev / TS"]
    subgraph 36699["External Systems"]
        36713["Database APIs<br>Supabase"]
        36714["Financial Data APIs<br>Plaid, GoCardless, etc."]
        36715["AI APIs<br>OpenAI, Mistral, etc."]
        36716["Messaging APIs<br>Resend, Novu, etc."]
        36717["Job Scheduler<br>Trigger.dev"]
    end
    subgraph 36700["Shared Libraries"]
        36709["UI Components<br>React / Tailwind"]
        36710["Supabase Client<br>TypeScript"]
        36711["Document Processing<br>TypeScript / AI"]
        36712["Notification Services<br>TypeScript"]
    end
    subgraph 36701["Financial Engine<br>Cloudflare Worker"]
        36707["Engine Worker<br>Hono"]
        36708["Financial Data Providers<br>TypeScript"]
        %% Edges at this level (grouped by source)
        36707["Engine Worker<br>Hono"] -->|uses| 36708["Financial Data Providers<br>TypeScript"]
    end
    subgraph 36702["Main Application<br>Next.js / Hono"]
        36703["Dashboard UI<br>Next.js / React"]
        36704["Server Actions<br>Next.js"]
        36705["API Routers<br>tRPC / Hono"]
        36706["Database Queries<br>Drizzle ORM"]
        %% Edges at this level (grouped by source)
        36703["Dashboard UI<br>Next.js / React"] -->|calls| 36705["API Routers<br>tRPC / Hono"]
        36704["Server Actions<br>Next.js"] -->|calls| 36705["API Routers<br>tRPC / Hono"]
        36705["API Routers<br>tRPC / Hono"] -->|uses| 36706["Database Queries<br>Drizzle ORM"]
    end
    %% Edges at this level (grouped by source)
    36718["User<br>External Actor"] --> 36703["Dashboard UI<br>Next.js / React"]
    36718["User<br>External Actor"] --> 36719["Desktop Application<br>Tauri / React"]
    36718["User<br>External Actor"] --> 36720["Public Websites<br>Next.js / Mintlify"]
    36719["Desktop Application<br>Tauri / React"] -->|loads| 36703["Dashboard UI<br>Next.js / React"]
    36703["Dashboard UI<br>Next.js / React"] -->|uses| 36709["UI Components<br>React / Tailwind"]
    36703["Dashboard UI<br>Next.js / React"] -->|authenticates with| 36710["Supabase Client<br>TypeScript"]
    36704["Server Actions<br>Next.js"] -->|invokes| 36707["Engine Worker<br>Hono"]
    36704["Server Actions<br>Next.js"] -->|uses| 36715["AI APIs<br>OpenAI, Mistral, etc."]
    36705["API Routers<br>tRPC / Hono"] -->|uses| 36712["Notification Services<br>TypeScript"]
    36705["API Routers<br>tRPC / Hono"] -->|schedules| 36717["Job Scheduler<br>Trigger.dev"]
    36721["Background Job Tasks<br>Trigger.dev / TS"] -->|syncs via| 36707["Engine Worker<br>Hono"]
    36721["Background Job Tasks<br>Trigger.dev / TS"] -->|accesses data via| 36710["Supabase Client<br>TypeScript"]
    36721["Background Job Tasks<br>Trigger.dev / TS"] -->|uses| 36711["Document Processing<br>TypeScript / AI"]
    36721["Background Job Tasks<br>Trigger.dev / TS"] -->|sends notifications via| 36712["Notification Services<br>TypeScript"]
    36706["Database Queries<br>Drizzle ORM"] -->|uses| 36710["Supabase Client<br>TypeScript"]
    36710["Supabase Client<br>TypeScript"] -->|accesses| 36713["Database APIs<br>Supabase"]
    36708["Financial Data Providers<br>TypeScript"] -->|calls| 36714["Financial Data APIs<br>Plaid, GoCardless, etc."]
    36711["Document Processing<br>TypeScript / AI"] -->|uses| 36715["AI APIs<br>OpenAI, Mistral, etc."]
    36712["Notification Services<br>TypeScript"] -->|sends via| 36716["Messaging APIs<br>Resend, Novu, etc."]
    36720["Public Websites<br>Next.js / Mintlify"] -->|subscribes via| 36716["Messaging APIs<br>Resend, Novu, etc."]
    36717["Job Scheduler<br>Trigger.dev"] -->|invokes| 36721["Background Job Tasks<br>Trigger.dev / TS"]
</turborepo>

<monorepo>
graph TD

    64092["User<br>External Actor"]
    64110["PostgreSQL Database<br>SQL, PostgreSQL"]
    64111["Redis Cache<br>Redis"]
    64112["LLM APIs<br>Cohere, Azure, AWS Bedrock, SageMaker"]
    64113["Search APIs<br>Brave, Tavily, Google Search"]
    64114["External Tools APIs<br>GitHub, Gmail, Google Drive, SharePoint, Slack"]
    64115["Text-to-Speech APIs<br>Google Cloud Text-to-Speech"]
    64116["Slack Platform<br>Slack API"]
    64117["slackBotCommands"]
    subgraph 64086["Community Extensions<br>Python"]
        64108["Model Deployments<br>Python"]
        64109["Tool Integrations<br>Python"]
    end
    subgraph 64087["Slack Bot<br>Node.js, TypeScript"]
        64105["Bot Entrypoint<br>Node.js"]
        64106["Event Handlers<br>Node.js"]
        64107["API Client<br>TypeScript"]
        %% Edges at this level (grouped by source)
        64105["Bot Entrypoint<br>Node.js"] -->|orchestrates| 64106["Event Handlers<br>Node.js"]
        64105["Bot Entrypoint<br>Node.js"] -->|calls| 64107["API Client<br>TypeScript"]
    end
    subgraph 64088["Coral Web App<br>Next.js, React, TypeScript"]
        64103["Web App Entrypoint<br>Next.js"]
        64104["API Client<br>TypeScript"]
        %% Edges at this level (grouped by source)
        64103["Web App Entrypoint<br>Next.js"] -->|calls| 64104["API Client<br>TypeScript"]
    end
    subgraph 64089["Assistants Web App<br>Next.js, React, TypeScript"]
        64101["Web App Entrypoint<br>Next.js"]
        64102["API Client<br>TypeScript"]
        %% Edges at this level (grouped by source)
        64101["Web App Entrypoint<br>Next.js"] -->|calls| 64102["API Client<br>TypeScript"]
    end
    subgraph 64090["CLI Tool<br>Python"]
        64100["CLI Entrypoint<br>Python"]
    end
    subgraph 64091["Backend API<br>Python, FastAPI"]
        64093["API Entrypoint<br>Python"]
        64094["API Routers<br>Python"]
        64095["Core Services<br>Python"]
        64096["Database Operations<br>Python"]
        64097["Database Models<br>SQLAlchemy"]
        64098["Tool Integrations<br>Python"]
        64099["Model Deployments<br>Python"]
        %% Edges at this level (grouped by source)
        64094["API Routers<br>Python"] -->|orchestrates| 64095["Core Services<br>Python"]
        64094["API Routers<br>Python"] -->|accesses| 64096["Database Operations<br>Python"]
        64094["API Routers<br>Python"] -->|invokes| 64098["Tool Integrations<br>Python"]
        64094["API Routers<br>Python"] -->|manages| 64099["Model Deployments<br>Python"]
        64093["API Entrypoint<br>Python"] -->|serves API| 64094["API Routers<br>Python"]
        64095["Core Services<br>Python"] -->|persists data| 64096["Database Operations<br>Python"]
        64095["Core Services<br>Python"] -->|uses| 64098["Tool Integrations<br>Python"]
        64095["Core Services<br>Python"] -->|uses| 64099["Model Deployments<br>Python"]
        64096["Database Operations<br>Python"] -->|maps to| 64097["Database Models<br>SQLAlchemy"]
    end
    %% Edges at this level (grouped by source)
    64094["API Routers<br>Python"] -->|uses| 64086["Community Extensions<br>Python"]
    64102["API Client<br>TypeScript"] -->|makes API requests| 64094["API Routers<br>Python"]
    64104["API Client<br>TypeScript"] -->|makes API requests| 64094["API Routers<br>Python"]
    64107["API Client<br>TypeScript"] -->|makes API requests| 64094["API Routers<br>Python"]
    64100["CLI Entrypoint<br>Python"] -->|configures| 64095["Core Services<br>Python"]
    64100["CLI Entrypoint<br>Python"] -->|migrates| 64097["Database Models<br>SQLAlchemy"]
    64095["Core Services<br>Python"] -->|caches| 64111["Redis Cache<br>Redis"]
    64095["Core Services<br>Python"] -->|synthesizes speech| 64115["Text-to-Speech APIs<br>Google Cloud Text-to-Speech"]
    64096["Database Operations<br>Python"] -->|stores/retrieves data| 64110["PostgreSQL Database<br>SQL, PostgreSQL"]
    64092["User<br>External Actor"] -->|executes| 64100["CLI Entrypoint<br>Python"]
    64092["User<br>External Actor"] -->|interacts via| 64101["Web App Entrypoint<br>Next.js"]
    64092["User<br>External Actor"] -->|interacts via| 64103["Web App Entrypoint<br>Next.js"]
    64092["User<br>External Actor"] -->|interacts via| 64105["Bot Entrypoint<br>Node.js"]
    64086["Community Extensions<br>Python"] -->|uses for models| 64112["LLM APIs<br>Cohere, Azure, AWS Bedrock, SageMaker"]
    64086["Community Extensions<br>Python"] -->|uses for tools| 64114["External Tools APIs<br>GitHub, Gmail, Google Drive, SharePoint, Slack"]
    64098["Tool Integrations<br>Python"] -->|uses for specific tasks| 64112["LLM APIs<br>Cohere, Azure, AWS Bedrock, SageMaker"]
    64098["Tool Integrations<br>Python"] -->|integrates with| 64113["Search APIs<br>Brave, Tavily, Google Search"]
    64098["Tool Integrations<br>Python"] -->|integrates with| 64114["External Tools APIs<br>GitHub, Gmail, Google Drive, SharePoint, Slack"]
    64099["Model Deployments<br>Python"] -->|connects to| 64112["LLM APIs<br>Cohere, Azure, AWS Bedrock, SageMaker"]
    64106["Event Handlers<br>Node.js"] -->|responds to events| 64116["Slack Platform<br>Slack API"]
    64106["Event Handlers<br>Node.js"] -->|processes commands| 64117["slackBotCommands"]
</monorepo>

</examples>

Now generate an architecture diagram based on the AST files following the formats presented in the examples.

<ast>{{ast}}</ast>

Remember:
1. Identify Architecture Type: Monorepo, microservices, or monolith
2. Find Entry Points: User-facing components and external actors. User should NOT be in a subgraph.
3. Group Components: Aggregate files into logical components based on their file paths
4. Create Simple Subgraph Structure: 
   - For monorepos: Create 3 top-level subgraphs: Applications, Packages, External Services
   - Each app/package becomes a single node directly in its parent subgraph
   - No nested subgraphs - keep it simple and flat
5. Map Relationships: Analyze imports and API calls between components
6. Add External Systems: Identify all third-party dependencies in a separate top-level subgraph
7. Generate Clean Output: Follow exact JSON schema with simple, flat subgraph structure

IMPORTANT:
- KEEP IT SIMPLE: Focus only on the most essential components and relationships.
- DO NOT OVERCOMPLICATE: Avoid creating unnecessary nodes or complex relationships

A simpler, clearer diagram is always better than a complex one. Focus on the core architecture that would help someone understand the system at a glance.