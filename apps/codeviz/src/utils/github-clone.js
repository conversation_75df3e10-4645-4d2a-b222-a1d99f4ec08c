import simpleGit from 'simple-git';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Extract repository information from GitHub URL
 * @param {string} url - GitHub repository URL
 * @returns {object} Repository info with owner, name, and clone URL
 */
export function parseGitHubUrl(url) {
  const patterns = [
    /^https:\/\/github\.com\/([^\/]+)\/([^\/]+?)(?:\.git)?(?:\/.*)?$/,
    /^git@github\.com:([^\/]+)\/([^\/]+?)(?:\.git)?$/,
    /^([^\/]+)\/([^\/]+)$/ // shorthand: owner/repo
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      const [, owner, name] = match;
      return {
        owner,
        name: name.replace(/\.git$/, ''),
        cloneUrl: `https://github.com/${owner}/${name}.git`,
        fullName: `${owner}/${name}`
      };
    }
  }

  throw new Error(`Invalid GitHub URL: ${url}`);
}

/**
 * Clone a GitHub repository to a temporary directory
 * @param {string} repoUrl - GitHub repository URL
 * @param {object} options - Options for cloning
 * @returns {object} Object with repository info and local path
 */
export async function cloneRepository(repoUrl, options = {}) {
  const {
    depth = 1,
    branch = null,
    tempDir = path.join(__dirname, '../../temp')
  } = options;

  const repoInfo = parseGitHubUrl(repoUrl);
  const localPath = path.join(tempDir, `${repoInfo.owner}-${repoInfo.name}-${Date.now()}`);

  try {
    // Ensure temp directory exists
    await fs.ensureDir(tempDir);

    // Clone options
    const cloneOptions = ['--depth', depth.toString()];
    if (branch) {
      cloneOptions.push('--branch', branch);
    }

    console.log(`Cloning ${repoInfo.fullName} to ${localPath}...`);
    
    const git = simpleGit();
    await git.clone(repoInfo.cloneUrl, localPath, cloneOptions);

    console.log(`Successfully cloned ${repoInfo.fullName}`);

    return {
      repoInfo,
      localPath,
      cleanup: async () => {
        try {
          await fs.remove(localPath);
          console.log(`Cleaned up temporary directory: ${localPath}`);
        } catch (error) {
          console.warn(`Failed to cleanup directory ${localPath}:`, error.message);
        }
      }
    };
  } catch (error) {
    // Cleanup on failure
    try {
      await fs.remove(localPath);
    } catch (cleanupError) {
      // Ignore cleanup errors
    }

    if (error.message.includes('Repository not found')) {
      throw new Error(`Repository ${repoInfo.fullName} not found or not accessible`);
    } else if (error.message.includes('Authentication failed')) {
      throw new Error(`Authentication failed for ${repoInfo.fullName}. Make sure you have access to private repositories.`);
    } else {
      throw new Error(`Failed to clone repository: ${error.message}`);
    }
  }
}

/**
 * Validate if a directory contains a valid repository
 * @param {string} localPath - Path to the repository directory
 * @returns {boolean} True if valid repository
 */
export async function validateRepository(localPath) {
  try {
    const stats = await fs.stat(localPath);
    if (!stats.isDirectory()) {
      return false;
    }

    // Check if it contains typical code files
    const files = await fs.readdir(localPath);
    const codeFiles = files.filter(file => 
      /\.(js|jsx|ts|tsx|py|java|cpp|c|cs|go|rs|php|rb)$/i.test(file) ||
      ['package.json', 'requirements.txt', 'pom.xml', 'Cargo.toml', 'composer.json'].includes(file)
    );

    return codeFiles.length > 0;
  } catch (error) {
    return false;
  }
}