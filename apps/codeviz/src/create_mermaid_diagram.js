#!/usr/bin/env node

/**
 * Mermaid Diagram Generator with Aspect Ratio Control
 *
 * Creates Mermaid diagrams from architecture JSON with intelligent aspect ratio optimization
 * to prevent diagrams from becoming too wide.
 *
 * CLI Usage:
 *   node create_mermaid_diagram.js [architecture.json] [output.mmd]
 *
 * Environment Variables for Aspect Ratio Control:
 *   ASPECT_RATIO_ENABLED=false    # Disable aspect ratio optimization
 *   MAX_WIDTH=8                   # Set maximum nodes per row (default: 10)
 *   FORCE_VERTICAL=true          # Force vertical (top-down) layout
 *
 * Examples:
 *   # Standard generation with aspect ratio control
 *   node create_mermaid_diagram.js architecture.json diagram.mmd
 *
 *   # Force narrow vertical layout
 *   FORCE_VERTICAL=true MAX_WIDTH=6 node create_mermaid_diagram.js architecture.json
 *
 *   # Disable aspect ratio control (original behavior)
 *   ASPECT_RATIO_ENABLED=false node create_mermaid_diagram.js architecture.json
 */

import fs from "fs-extra";
import path from "path";
import {
  createMermaidDiagram,
  wrapInMarkdown,
} from "./utils/mermaid-builder.js";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Load and validate architecture data
 * @param {string} architecturePath - Path to architecture JSON file
 * @returns {object} Loaded architecture data
 */
async function loadArchitecture(architecturePath) {
  try {
    const architecture = await fs.readJson(architecturePath);

    // Validate required structure
    if (!architecture.nodes || !Array.isArray(architecture.nodes)) {
      throw new Error('Architecture file must contain a "nodes" array');
    }

    if (!architecture.edges || !Array.isArray(architecture.edges)) {
      throw new Error('Architecture file must contain an "edges" array');
    }

    console.log(
      `Loaded architecture with ${architecture.nodes.length} nodes and ${architecture.edges.length} edges`,
    );

    if (architecture.metadata) {
      console.log(`Generated on: ${architecture.metadata.generatedAt}`);
      console.log(`Source: ${architecture.metadata.sourceIndex}`);
    }

    return architecture;
  } catch (error) {
    if (error.code === "ENOENT") {
      throw new Error(
        `Architecture file not found: ${architecturePath}\nRun generate_relationships.js first to generate the architecture data.`,
      );
    }
    throw new Error(`Failed to load architecture: ${error.message}`);
  }
}

/**
 * Validate architecture before diagram generation
 * @param {object} architecture - Architecture data
 * @returns {object} Validation result
 */
function validateArchitecture(architecture) {
  const issues = [];
  const warnings = [];

  // Check nodes
  const nodeIds = new Set();
  architecture.nodes.forEach((node, index) => {
    if (!node.id) {
      issues.push(`Node ${index} missing required 'id' field`);
    } else if (nodeIds.has(node.id)) {
      issues.push(`Duplicate node ID: ${node.id}`);
    } else {
      nodeIds.add(node.id);
    }

    if (!node.label) {
      warnings.push(`Node ${node.id || index} missing label`);
    }
  });

  // Check edges
  architecture.edges.forEach((edge, index) => {
    if (!edge.source) {
      issues.push(`Edge ${index} missing required 'source' field`);
    } else if (!nodeIds.has(edge.source)) {
      issues.push(
        `Edge ${index} references non-existent source node: ${edge.source}`,
      );
    }

    if (!edge.target) {
      issues.push(`Edge ${index} missing required 'target' field`);
    } else if (!nodeIds.has(edge.target)) {
      issues.push(
        `Edge ${index} references non-existent target node: ${edge.target}`,
      );
    }

    if (!edge.label) {
      warnings.push(
        `Edge ${index} (${edge.source} -> ${edge.target}) missing label`,
      );
    }
  });

  // Check for isolated nodes
  const connectedNodes = new Set();
  architecture.edges.forEach((edge) => {
    connectedNodes.add(edge.source);
    connectedNodes.add(edge.target);
  });

  const isolatedNodes = architecture.nodes.filter(
    (node) => !connectedNodes.has(node.id),
  );
  if (isolatedNodes.length > 0) {
    warnings.push(
      `${isolatedNodes.length} isolated nodes: ${isolatedNodes.map((n) => n.id).join(", ")}`,
    );
  }

  return {
    valid: issues.length === 0,
    issues,
    warnings,
  };
}

/**
 * Optimize architecture for better diagram layout
 * @param {object} architecture - Original architecture
 * @returns {object} Optimized architecture
 */
function optimizeArchitecture(architecture) {
  const optimized = JSON.parse(JSON.stringify(architecture)); // Deep clone

  // Ensure all nodes have proper types for better styling
  optimized.nodes.forEach((node) => {
    if (!node.type) {
      // Infer type from label or ID
      const label = (node.label || "").toLowerCase();
      const id = (node.id || "").toLowerCase();

      if (
        label.includes("ui") ||
        label.includes("frontend") ||
        label.includes("react") ||
        label.includes("vue")
      ) {
        node.type = "Frontend";
      } else if (
        label.includes("api") ||
        label.includes("backend") ||
        label.includes("server")
      ) {
        node.type = "Backend";
      } else if (
        label.includes("database") ||
        label.includes("db") ||
        id.includes("db")
      ) {
        node.type = "External";
      } else if (label.includes("service") || label.includes("auth")) {
        node.type = "Service";
      } else if (label.includes("user") || label.includes("actor")) {
        node.type = "Actor";
      } else {
        node.type = "Component";
      }
    }
  });

  // Optimize subgraphs if not provided
  if (!optimized.subgraphs || optimized.subgraphs.length === 0) {
    optimized.subgraphs = generateOptimalSubgraphs(optimized.nodes);
  }

  return optimized;
}

/**
 * Generate optimal subgraphs based on node types and relationships
 * @param {Array} nodes - Array of nodes
 * @returns {Array} Generated subgraphs
 */
function generateOptimalSubgraphs(nodes) {
  const subgraphs = [];
  const typeGroups = {};

  // Group nodes by type
  nodes.forEach((node) => {
    const type = node.type || "Other";
    if (!typeGroups[type]) {
      typeGroups[type] = [];
    }
    typeGroups[type].push(node.id);
  });

  // Create subgraphs for groups with multiple nodes
  Object.entries(typeGroups).forEach(([type, nodeIds]) => {
    if (nodeIds.length > 1) {
      const subgraphLabels = {
        Frontend: "Client Application<br>User Interface",
        Backend: "Server-Side Logic<br>API & Business Logic",
        Service: "Core Services<br>Business Components",
        DataAccess: "Data Layer<br>Database & Storage",
        External: "External Systems<br>Third-party Services",
        Actor: "Users & Actors<br>External Entities",
      };

      subgraphs.push({
        id: type.toLowerCase(),
        label: subgraphLabels[type] || type,
        nodes: nodeIds,
      });
    }
  });

  return subgraphs;
}

/**
 * Generate enhanced markdown with additional documentation
 * @param {string} mermaidCode - Mermaid diagram code
 * @param {object} architecture - Architecture data
 * @param {object} options - Generation options
 * @returns {string} Enhanced markdown content
 */
function generateEnhancedMarkdown(mermaidCode, architecture, options = {}) {
  const {
    includeComponentList = true,
    includeRelationshipList = true,
    includeMetadata = true,
  } = options;

  const lines = [];

  // Title and description
  lines.push("# System Architecture Diagram");
  lines.push("");
  lines.push(
    "This diagram shows the high-level architecture of the system, including main components and their relationships.",
  );
  lines.push("");

  // Metadata
  if (includeMetadata && architecture.metadata) {
    lines.push("## Generation Info");
    lines.push(
      `- **Generated**: ${new Date(architecture.metadata.generatedAt).toLocaleString()}`,
    );
    lines.push(`- **Source**: ${architecture.metadata.sourceIndex}`);
    lines.push(`- **Provider**: ${architecture.metadata.provider}`);
    lines.push("");
  }

  // Main diagram
  lines.push("## Architecture Overview");
  lines.push("");
  lines.push("```mermaid");
  lines.push(mermaidCode);
  lines.push("```");
  lines.push("");

  // Component details
  if (includeComponentList) {
    lines.push("## Components");
    lines.push("");

    const componentsByType = {};
    architecture.nodes.forEach((node) => {
      const type = node.type || "Other";
      if (!componentsByType[type]) {
        componentsByType[type] = [];
      }
      componentsByType[type].push(node);
    });

    Object.entries(componentsByType).forEach(([type, nodes]) => {
      lines.push(`### ${type}`);
      lines.push("");
      nodes.forEach((node) => {
        const label = node.label.replace(/<br>/g, " - ");
        lines.push(`- **${node.id}**: ${label}`);
      });
      lines.push("");
    });
  }

  // Relationship details
  if (includeRelationshipList) {
    lines.push("## Relationships");
    lines.push("");
    architecture.edges.forEach((edge) => {
      const sourceNode = architecture.nodes.find((n) => n.id === edge.source);
      const targetNode = architecture.nodes.find((n) => n.id === edge.target);

      const sourceName = sourceNode?.label?.split("<br>")[0] || edge.source;
      const targetName = targetNode?.label?.split("<br>")[0] || edge.target;

      lines.push(`- **${sourceName}** ${edge.label} **${targetName}**`);
    });
    lines.push("");
  }

  // Legend
  lines.push("## Legend");
  lines.push("");
  lines.push("### Component Types");
  lines.push("- **Rectangles**: Application components and services");
  lines.push("- **Rounded rectangles**: External systems and databases");
  lines.push("- **Circles**: Users and external actors");
  lines.push("");
  lines.push("### Relationships");
  lines.push("- **Arrows**: Data flow, API calls, and dependencies");
  lines.push("- **Labels**: Describe the nature of the relationship");
  lines.push("");

  // Viewing instructions
  lines.push("## Viewing This Diagram");
  lines.push("");
  lines.push("This Mermaid diagram can be viewed in:");
  lines.push("- GitHub (native support)");
  lines.push("- GitLab (native support)");
  lines.push("- VS Code (with Mermaid extensions)");
  lines.push("- Mermaid Live Editor (https://mermaid.live)");
  lines.push("- Any Markdown editor with Mermaid support");

  return lines.join("\n");
}

/**
 * Main function to create Mermaid diagram
 * @param {string} architecturePath - Path to architecture JSON file
 * @param {object} options - Generation options
 * @returns {string} Generated diagram path
 */
export async function createMermaidDiagramFile(
  architecturePath = "architecture.json",
  options = {},
) {
  const {
    outputPath = "architecture.mmd",
    optimize = true,
    validate = true,
    enhanced = false,
    verbose = false,
    // Aspect ratio control options
    aspectRatioControl = {
      enabled: true,
      preferredRatio: 1.5,
      maxWidth: 10,
      autoDirection: true,
      forceVertical: true,
      compactSubgraphs: true,
    },
  } = options;

  try {
    // Load architecture data
    const architecture = await loadArchitecture(architecturePath);

    // Validate architecture if requested
    if (validate) {
      const validation = validateArchitecture(architecture);

      if (validation.warnings.length > 0 && verbose) {
        console.warn("Architecture validation warnings:");
        validation.warnings.forEach((warning) =>
          console.warn(`  - ${warning}`),
        );
      }

      if (!validation.valid) {
        console.error("Architecture validation failed:");
        validation.issues.forEach((issue) => console.error(`  - ${issue}`));
        throw new Error(
          "Architecture validation failed. Please fix the issues and try again.",
        );
      }

      if (verbose) {
        console.log("Architecture validation passed");
      }
    }

    // Optimize architecture if requested
    const finalArchitecture = optimize
      ? optimizeArchitecture(architecture)
      : architecture;

    if (verbose && optimize) {
      console.log("Architecture optimized for better diagram layout");
    }

    // Generate Mermaid diagram
    console.log("Generating Mermaid diagram...");

    if (verbose && aspectRatioControl.enabled) {
      console.log("Aspect ratio control enabled with settings:", {
        preferredRatio: aspectRatioControl.preferredRatio,
        maxWidth: aspectRatioControl.maxWidth,
        autoDirection: aspectRatioControl.autoDirection,
        forceVertical: aspectRatioControl.forceVertical,
        compactSubgraphs: aspectRatioControl.compactSubgraphs,
      });
    }

    const mermaidCode = createMermaidDiagram(finalArchitecture, {
      startId: 5000, // Start IDs from 5000 to match reference output
      aspectRatioControl: aspectRatioControl,
    });

    // Generate content based on format
    const fileExtension = path.extname(outputPath);
    let fileContent;

    if (fileExtension === ".mmd") {
      // Direct Mermaid file
      fileContent = mermaidCode;
    } else {
      // Markdown file (legacy support)
      fileContent = enhanced
        ? generateEnhancedMarkdown(mermaidCode, finalArchitecture, options)
        : wrapInMarkdown(mermaidCode, {
            title: "Architecture Diagram",
            description:
              "System architecture overview showing components and relationships.",
          });
    }

    // Save to file
    await fs.writeFile(outputPath, fileContent);
    console.log(`\nMermaid diagram saved to: ${outputPath}`);

    if (verbose) {
      console.log(
        `Generated diagram with ${finalArchitecture.nodes.length} nodes and ${finalArchitecture.edges.length} edges`,
      );

      if (finalArchitecture.subgraphs) {
        console.log(
          `Organized into ${finalArchitecture.subgraphs.length} subgraphs`,
        );
      }
    }

    return outputPath;
  } catch (error) {
    console.error("Failed to create Mermaid diagram:", error.message);
    throw error;
  }
}

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
  const architecturePath = process.argv[2] || "architecture.json";
  const outputPath = process.argv[3] || "architecture.mmd";

  // Check for aspect ratio configuration from environment or command line
  const aspectRatioEnabled = process.env.ASPECT_RATIO_ENABLED !== "false";
  const maxWidth = parseInt(process.env.MAX_WIDTH) || 10;
  const forceVertical = process.env.FORCE_VERTICAL === "true";

  try {
    await createMermaidDiagramFile(architecturePath, {
      outputPath,
      optimize: true,
      validate: true,
      enhanced: true,
      verbose: true,
      aspectRatioControl: {
        enabled: aspectRatioEnabled,
        preferredRatio: 1.5,
        maxWidth: maxWidth,
        autoDirection: !forceVertical,
        forceVertical: forceVertical,
        compactSubgraphs: true,
      },
    });
  } catch (error) {
    console.error("Error:", error.message);
    process.exit(1);
  }
}
