#!/usr/bin/env node

import "dotenv/config";
import { program } from "commander";
import chalk from "chalk";
import ora from "ora";
import fs from "fs-extra";
import path from "path";
import { fileURLToPath } from "url";

// Import our pipeline modules
import { cloneRepository, parseGitHubUrl } from "./src/utils/github-clone.js";
import { createCodeIndex } from "./src/create_ast_index.js";
import { generateRelationships } from "./src/generate_relationships.js";
import { createMermaidDiagramFile } from "./src/create_mermaid_diagram.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Package info
const packageInfo = await fs.readJson(path.join(__dirname, "package.json"));

/**
 * Find an existing repository clone in the temp directory
 * @param {string} tempDir - Path to temp directory
 * @param {object} repoInfo - Repository info with owner and name
 * @returns {Promise<string|null>} - Path to existing repo or null if not found
 */
async function findExistingRepository(tempDir, repoInfo) {
  try {
    // Check if temp directory exists
    if (!(await fs.pathExists(tempDir))) {
      return null;
    }

    // Read all directories in temp
    const entries = await fs.readdir(tempDir, { withFileTypes: true });
    const directories = entries.filter((entry) => entry.isDirectory());

    // Look for directories that match the repo name pattern
    // Common patterns: repo-name, owner-repo-name, owner-repo-name-timestamp
    const repoPattern = new RegExp(
      `^(${repoInfo.owner}-)?${repoInfo.name}(-\\d+)?$`,
      "i",
    );

    const matchingDirs = directories
      .filter((dir) => repoPattern.test(dir.name))
      .map((dir) => ({
        name: dir.name,
        path: path.join(tempDir, dir.name),
        // Extract timestamp if present for sorting
        timestamp: dir.name.match(/-(\d+)$/)?.[1] || "0",
      }))
      .sort((a, b) => parseInt(b.timestamp) - parseInt(a.timestamp)); // Most recent first

    // Return the most recent match if any
    if (matchingDirs.length > 0) {
      const selectedDir = matchingDirs[0];

      // Verify it's actually a git repository
      const gitPath = path.join(selectedDir.path, ".git");
      if (await fs.pathExists(gitPath)) {
        return selectedDir.path;
      }
    }

    return null;
  } catch (error) {
    console.warn(
      chalk.yellow(
        `Warning: Could not search for existing repositories: ${error.message}`,
      ),
    );
    return null;
  }
}

/**
 * Setup CLI program
 */
program
  .name("codeviz")
  .description(
    "Generate architecture diagrams from GitHub repositories using AST analysis and LLM",
  )
  .version(packageInfo.version)
  .argument(
    "<github-url>",
    "GitHub repository URL (e.g., https://github.com/user/repo)",
  )
  .action(async (githubUrl) => {
    await runAnalysis(githubUrl);
  });

/**
 * Main analysis pipeline
 */
async function runAnalysis(githubUrl) {
  let spinner;
  let repoPath = null;

  try {
    // Validate GitHub URL
    const repoInfo = parseGitHubUrl(githubUrl);
    console.log(
      chalk.blue(`🔍 Analyzing repository: ${chalk.bold(repoInfo.fullName)}`),
    );

    // Setup paths
    const tempDir = path.join(__dirname, "temp");
    const outputDir = path.join(__dirname, "output", repoInfo.name);

    // Ensure directories exist
    await fs.ensureDir(tempDir);
    await fs.ensureDir(outputDir);

    // Step 1: Get repository (reuse if exists, else clone)
    const existingRepoPath = await findExistingRepository(tempDir, repoInfo);

    if (existingRepoPath) {
      console.log(
        chalk.green(`📂 Reusing existing repository at ${existingRepoPath}`),
      );
      repoPath = existingRepoPath;
    } else {
      spinner = ora("Cloning repository...").start();
      try {
        const cloneResult = await cloneRepository(githubUrl, {
          depth: 1,
          tempDir: tempDir,
        });
        repoPath = cloneResult.localPath;
        spinner.succeed(`Repository cloned to ${repoPath}`);
      } catch (error) {
        spinner.fail(`Failed to clone repository: ${error.message}`);
        throw error;
      }
    }

    // Step 2: Create AST index
    spinner = ora("Analyzing codebase and creating AST index...").start();
    const indexPath = path.join(outputDir, "code_index.json");
    try {
      await createCodeIndex(repoPath, {
        outputPath: indexPath,
      });
      spinner.succeed(`AST index created: ${indexPath}`);
    } catch (error) {
      spinner.fail(`Failed to create AST index: ${error.message}`);
      throw error;
    }

    // Step 3: Generate relationships
    spinner = ora("Generating architecture relationships using LLM...").start();
    const architecturePath = path.join(outputDir, "architecture.json");
    try {
      await generateRelationships(indexPath, {
        outputPath: architecturePath,
      });
      spinner.succeed(
        `Architecture relationships generated: ${architecturePath}`,
      );
    } catch (error) {
      spinner.fail(`Failed to generate relationships: ${error.message}`);
      throw error;
    }

    // Step 4: Create Mermaid diagram
    spinner = ora("Creating Mermaid diagram...").start();
    const diagramPath = path.join(outputDir, "architecture.mmd");
    try {
      await createMermaidDiagramFile(architecturePath, {
        outputPath: diagramPath,
        optimize: true,
        validate: true,
        enhanced: true,
      });
      spinner.succeed(`Mermaid diagram created: ${diagramPath}`);
    } catch (error) {
      spinner.fail(`Failed to create diagram: ${error.message}`);
      throw error;
    }

    // Success summary
    console.log(chalk.green("\n✅ Analysis completed successfully!"));
    console.log(chalk.blue("\n📁 Generated files:"));
    console.log(`  - ${chalk.cyan(indexPath)} - AST analysis data`);
    console.log(
      `  - ${chalk.cyan(architecturePath)} - Architecture relationships`,
    );
    console.log(`  - ${chalk.cyan(diagramPath)} - Mermaid diagram`);

    console.log(chalk.blue("\n🎯 Next steps:"));
    console.log(
      "  - Open the .mmd file in any Markdown viewer with Mermaid support",
    );
    console.log("  - View directly on GitHub, GitLab, or VS Code");
    console.log("  - Use https://mermaid.live for online viewing");
  } catch (error) {
    if (spinner) {
      spinner.fail(`Operation failed: ${error.message}`);
    } else {
      console.error(chalk.red(`Error: ${error.message}`));
    }
    process.exit(1);
  }
}

/**
 * Display banner and environment info
 */
function displayBanner() {
  console.log(
    chalk.blue(`
╔══════════════════════════════════════╗
║               CodeViz                ║
║   Architecture Diagram Generator     ║
╚══════════════════════════════════════╝
`),
  );
  console.log(chalk.gray(`Version: ${packageInfo.version}`));

  // Check for OpenAI API key
  const hasOpenAI = !!process.env.OPENAI_API_KEY;

  if (!hasOpenAI) {
    console.log(
      chalk.yellow(
        "\n⚠️  No OpenAI API key detected. Set OPENAI_API_KEY environment variable.\n",
      ),
    );
  } else {
    console.log(chalk.green("✅ OpenAI API key found\n"));
  }
}

// Handle errors gracefully
process.on("unhandledRejection", (error) => {
  console.error(chalk.red(`\n❌ Unhandled error: ${error.message}`));
  process.exit(1);
});

process.on("SIGINT", () => {
  console.log(chalk.yellow("\n\n⏹️  Operation cancelled by user"));
  process.exit(0);
});

// Show banner for main command
if (
  process.argv.length === 2 ||
  (process.argv.length === 3 && process.argv[2] === "--help")
) {
  displayBanner();
}

// Parse command line arguments
program.parse();
