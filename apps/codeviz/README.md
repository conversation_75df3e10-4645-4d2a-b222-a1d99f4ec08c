# CodeViz 🏗️

Generate sophisticated architecture diagrams from GitHub repositories using AST analysis and LLM intelligence.

## Overview

CodeViz is a powerful tool that automatically analyzes GitHub repositories and creates beautiful, insightful architecture diagrams. It combines static code analysis using Abstract Syntax Trees (AST) with Large Language Model (LLM) intelligence to understand your codebase structure and generate Mermaid diagrams that clearly show:

- **System Components**: UI layers, services, databases, external systems
- **Relationships**: Data flow, API calls, dependencies
- **Technology Stack**: Frameworks, libraries, and tools used
- **Architecture Patterns**: Layered organization with subgraphs

## Features

✨ **Intelligent Analysis**: Uses Babel AST parsing to understand code structure  
🤖 **LLM-Powered**: Leverages Anthropic Claude or OpenAI GPT for architectural insights  
📊 **Professional Diagrams**: Generates Mermaid diagrams with subgraphs and styling  
🔗 **GitHub Integration**: Direct repository cloning and analysis  
⚡ **Multi-Format Support**: JavaScript, TypeScript, JSX, TSX files  
🎯 **Framework Detection**: Recognizes React, Next.js, Vue, Express, and more

## Installation

### Prerequisites

- Node.js 18+
- Git
- OpenAI API key

### Setup

```bash
# Clone the repository
git clone https://github.com/your-username/codeviz.git
cd codeviz

# Install dependencies
npm install

# Set up your OpenAI API key
export OPENAI_API_KEY="your-openai-key"
```

## Quick Start

### Analyze any GitHub repository:

```bash
# Basic usage
node main.js https://github.com/vercel/next.js

# With custom output directory
node main.js https://github.com/facebook/react -o ./my-analysis

# With verbose output for debugging
node main.js https://github.com/vuejs/vue --verbose
```

### The tool will generate:

- `code_index.json` - Detailed AST analysis
- `architecture.json` - LLM-generated architecture data
- `architecture.mmd` - Raw Mermaid diagram file

## Example Output

Here's what CodeViz generates for a Next.js application:

```mermaid
graph TD

    5770["User<br>External Actor"]
    subgraph 5758["External Systems"]
        5767["Database<br>PostgreSQL"]
        5768["AI APIs<br>OpenAI, Google, etc."]
        5769["Blob Storage<br>Vercel Blob"]
    end
    subgraph 5759["AI Chatbot Web App<br>Next.js"]
        5760["Chat UI<br>React / Next.js Pages"]
        5761["Auth UI<br>React / Next.js Pages"]
        5762["API Routes<br>Next.js Route Handlers"]
        5763["Authentication Service<br>NextAuth.js"]
        5764["AI Services & Tooling<br>TypeScript"]
        5765["Artifact Generation<br>TypeScript"]
        5766["Database Services<br>Drizzle ORM"]
    end

    5770 -->|Interacts with| 5760
    5760 -->|Sends requests to| 5762
    5762 -->|Calls| 5768
    5766 -->|Reads/Writes| 5767
```

## Advanced Usage

### Individual Pipeline Steps

Run each step of the analysis pipeline separately:

```bash
# 1. Create AST index from local repository
node main.js index ./my-repo --output code_index.json

# 2. Generate relationships from existing index
node main.js relationships code_index.json --output architecture.json

# 3. Create diagram from existing architecture
node main.js diagram architecture.json --output diagram.mmd
```

### Options

| Option             | Description                           | Default    |
| ------------------ | ------------------------------------- | ---------- |
| `-o, --output-dir` | Output directory for generated files  | `./output` |
| `--keep-repo`      | Keep cloned repository after analysis | `false`    |
| `--skip-cleanup`   | Skip cleanup of temporary files       | `false`    |
| `-v, --verbose`    | Enable verbose output                 | `false`    |
| `--no-optimize`    | Skip diagram optimization             | `false`    |
| `--no-validate`    | Skip validation steps                 | `false`    |

## How It Works

CodeViz uses a sophisticated 3-stage pipeline:

### 1. **AST Analysis** (`create_ast_index.js`)

- Recursively scans repository files
- Parses JavaScript/TypeScript with Babel
- Extracts imports, exports, functions, classes
- Detects frameworks and architectural patterns
- Generates structured `code_index.json`

### 2. **LLM Analysis** (`generate_relationships.js`)

- Summarizes code index for LLM processing
- Sends architectural analysis prompt to LLM
- LLM identifies components and relationships
- Validates and structures the response
- Outputs `architecture.json`

### 3. **Diagram Generation** (`create_mermaid_diagram.js`)

- Converts architecture data to Mermaid syntax
- Creates subgraphs for logical grouping
- Adds styling and proper formatting
- Generates enhanced markdown with documentation
- Outputs `architecture.mmd`

## Supported Technologies

### Languages & Frameworks

**Frontend Languages:**

- **JavaScript/TypeScript**: `.js`, `.jsx`, `.ts`, `.tsx`, `.mjs`, `.cjs`
- **Frameworks**: React, Next.js, Vue.js, Nuxt.js, Angular

**Backend Languages:**

- **Python**: `.py`, `.pyi` - Django, Flask, FastAPI
- **Java**: `.java` - Spring Boot, Spring MVC
- **Go**: `.go` - Gin, Echo, Fiber
- **PHP**: `.php` - Laravel, Symfony, CodeIgniter
- **Ruby**: `.rb` - Ruby on Rails, Sinatra
- **Rust**: `.rs` - Actix, Rocket, Warp
- **Kotlin**: `.kt`, `.kts` - Spring Boot, Ktor
- **C#**: `.cs` - ASP.NET Core, .NET
- **Scala**: `.scala` - Play Framework, Akka
- **Clojure**: `.clj` - Ring, Compojure
- **Elixir**: `.ex`, `.exs` - Phoenix

**Database & ORM:**

- **Database**: Prisma, Drizzle ORM, Mongoose, Sequelize
- **Testing**: Jest, Vitest, Cypress

## Configuration

### Environment Variables

```bash
# OpenAI API Key
OPENAI_API_KEY=your-openai-key
```

### Multi-Language Parsing

CodeViz uses a sophisticated multi-language parsing system:

**Frontend Languages (JavaScript/TypeScript):**

- Uses Babel parser for accurate AST analysis
- Automatic JSX detection in `.js` files
- Full TypeScript support with type annotations

**Backend Languages:**

- **Primary**: Tree-sitter parsers for precise AST analysis
- **Fallback**: Regex-based parsing for maximum compatibility
- **Auto-detection**: Language identification by file extension

**Extracted Information:**

- Import/require statements and dependencies
- Function and method definitions
- Class, struct, and type definitions
- Export declarations and public APIs
- Language-specific patterns (Go capitalization, Rust pub keywords, etc.)

### Framework Detection

CodeViz automatically detects frameworks and patterns based on:

- Import statements (`import React from 'react'`, `from django import`)
- File patterns (`pages/`, `api/`, `components/`, `views/`, `controllers/`)
- Configuration files (`next.config.js`, `package.json`, `requirements.txt`)
- Code patterns (JSX, API routes, middleware, decorators)

## Troubleshooting

### Common Issues

**"No OpenAI API key found"**

```bash
# Set your API key
export OPENAI_API_KEY="your-key-here"
```

**"Repository not found"**

- Check the GitHub URL is correct
- Ensure repository is public or you have access
- Try with `https://` prefix

**"Failed to parse files"**

- Repository might have syntax errors
- Some files may be too large or complex
- Check verbose output: `node main.js <url> --verbose`

### Testing

```bash
# Test LLM connectivity
node main.js test

# Verbose analysis
node main.js <github-url> --verbose
```

## Development

### Project Structure

```
codeviz/
├── src/
│   ├── utils/
│   │   ├── github-clone.js     # Repository cloning
│   │   ├── ast-parser.js       # Babel AST analysis
│   │   ├── llm-client.js       # LLM API wrapper
│   │   └── mermaid-builder.js  # Diagram generation
│   ├── create_ast_index.js     # Step 1: AST analysis
│   ├── generate_relationships.js # Step 2: LLM analysis
│   └── create_mermaid_diagram.js # Step 3: Diagram creation
├── main.js                     # CLI interface
└── package.json
```

### Contributing

1. Fork the repository
2. Create your feature branch: `git checkout -b my-new-feature`
3. Commit your changes: `git commit -am 'Add some feature'`
4. Push to the branch: `git push origin my-new-feature`
5. Submit a pull request

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Credits

Built with:

- [Babel](https://babeljs.io/) for AST parsing
- [OpenAI](https://openai.com/) for LLM analysis
- [Mermaid](https://mermaid.js.org/) for diagram generation
- [Commander.js](https://github.com/tj/commander.js/) for CLI interface

---

**Happy diagramming!** 🎨📊
